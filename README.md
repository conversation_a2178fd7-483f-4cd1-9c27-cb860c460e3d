# 🎯 智能英语词汇消消乐

一个基于机器学习的智能英语词汇学习游戏，通过配对英文单词和中文释义来高效学习词汇。

## ✨ 核心特色

### 🧠 智能学习系统
- **自适应算法**: 根据你的学习表现智能调整词汇出现频率
- **个性化学习**: 掌握度高的词汇减少出现，需要练习的词汇增加出现
- **学习记录**: 完整追踪每个词汇的学习情况和掌握程度
- **数据持久化**: 学习进度本地保存，永不丢失

### 🎮 游戏体验
- **无关卡限制**: 每次游戏随机选择18个词汇，专注学习效果
- **实时反馈**: 动画效果、音效提示、粒子效果
- **简约设计**: 现代化UI界面，专注学习内容
- **完整统计**: 学习进度、掌握情况、准确率追踪

## 🎮 游戏玩法

1. **点击开始游戏** - 系统智能选择需要练习的词汇
2. **配对学习** - 点击英文单词，再点击对应中文释义
3. **即时反馈** - 正确配对消除格子，错误配对摇摆提示
4. **连击奖励** - 连续正确配对获得额外分数
5. **智能调整** - 系统记录学习效果，自动优化后续词汇选择

## 🧠 智能学习原理

### 📊 学习数据追踪
- **正确率统计**: 实时计算每个词汇的掌握程度
- **学习历史**: 记录最近10次游戏中的表现
- **权重调整**: 基于学习表现动态调整词汇出现概率

### ⚡ 自适应算法
- **高掌握度词汇** (正确率>80%): 大幅降低出现概率 (0.3x)
- **中等掌握度词汇** (正确率50-80%): 适度调整出现频率
- **低掌握度词汇** (正确率<50%): 显著提高出现概率 (2.5x)
- **连续正确奖励**: 连续答对3次以上进一步降低出现频率

## 📖 词汇资源

### 完整词汇信息
- **650+精选词汇**: 涵盖四六级核心词汇，持续扩充中
- **标准音标**: 每个词汇都有国际音标发音指导
- **准确释义**: 精确的中文翻译和详细解释
- **智能分布**: 从基础高频词到高级词汇的完整覆盖

### 词汇来源
- 真实英语四六级考试词汇表
- 按字母顺序系统化整理
- 涵盖日常用词到学术专业词汇
- 经过教学验证的核心词汇集合

## 🎵 功能特色

### 视觉效果
- 流畅的CSS动画过渡
- 配对成功的粒子效果
- 连击提示和奖励显示
- 响应式设计，支持移动设备

### 音效系统
- 选择音效
- 配对成功音效
- 配对失败音效
- 关卡完成音效
- 提示音效
- 可切换音效开关

### 智能计分系统
- **基础分数**: 100分/正确配对
- **时间奖励**: 基于完成速度的额外分数
- **连击奖励**: 3连击及以上额外加分
- **准确率奖励**: 无错误完成获得50分奖励
- **提示惩罚**: 使用提示扣除20分

### 学习统计
- **实时准确率**: 当前游戏的配对准确率
- **总体进度**: 已学习词汇数量统计
- **掌握情况**: 掌握词汇 vs 需练习词汇
- **学习建议**: 基于数据的个性化学习指导

## 🎮 操作控制

### 鼠标操作
- 点击格子进行词汇配对
- 点击"📊 学习统计"查看学习进度
- 点击"💡 提示"获得配对提示

### 键盘快捷键
- `Esc`: 取消当前选择
- `Ctrl + H`: 显示提示
- `Ctrl + R`: 重新开始游戏

## 🛠️ 技术实现

- **纯前端实现**: HTML5 + CSS3 + JavaScript
- **响应式设计**: 支持桌面和移动设备
- **Web Audio API**: 实现音效系统
- **CSS动画**: 丰富的视觉反馈
- **模块化代码**: 清晰的代码结构

## 📁 文件结构

```
消消乐/
├── index.html          # 游戏主页面
├── style.css           # 样式文件
├── game.js             # 游戏逻辑
├── vocabulary.js       # 词汇数据
└── README.md          # 说明文档
```

## 🚀 如何开始

1. 下载或克隆项目到本地
2. 用浏览器打开 `index.html` 文件
3. 点击"开始游戏"即可开始游戏

## 🎯 学习目标

### 🧠 智能化学习体验
- **精准学习**: 专注于你需要加强的词汇
- **高效记忆**: 通过重复优化记忆曲线
- **个性化进度**: 每个人的学习路径都不同
- **数据驱动**: 基于学习表现的科学调整

### 📈 学习效果提升
- **词汇量增长**: 系统性掌握英语核心词汇
- **记忆力强化**: 通过智能重复巩固记忆
- **学习兴趣**: 游戏化体验保持学习动力
- **自我认知**: 清楚了解自己的学习进度

## 🔧 技术特色

### 智能算法
- **机器学习**: 基于用户行为的自适应算法
- **数据分析**: 实时分析学习效果和趋势
- **权重计算**: 动态调整词汇出现概率
- **本地存储**: 完整的学习数据持久化

### 前端技术
- **原生JavaScript**: 高性能的游戏逻辑实现
- **CSS3动画**: 流畅的视觉反馈效果
- **响应式设计**: 完美适配各种设备
- **Web Audio API**: 丰富的音效体验

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🚀 开始你的智能学习之旅

这不仅仅是一个游戏，更是一个智能的英语学习伙伴！

✨ **每次游戏都是个性化的学习体验**  
🎯 **系统会越来越了解你的学习需求**  
📈 **学习效果随着使用时间持续提升**  

立即开始，体验AI驱动的英语词汇学习革命！