// 音效系统
class SoundSystem {
    constructor() {
        this.audioContext = null;
        this.sounds = {};
        this.enabled = true;
        this.initialize();
    }
    
    initialize() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.createSounds();
        } catch (e) {
            console.log('Web Audio API not supported');
            this.enabled = false;
        }
    }
    
    createSounds() {
        // 创建各种音效的频率和持续时间配置
        this.soundConfigs = {
            select: { frequency: 800, duration: 0.1 },
            match: { frequency: 1200, duration: 0.2 },
            error: { frequency: 300, duration: 0.3 },
            levelComplete: { frequency: 1500, duration: 0.5 },
            hint: { frequency: 1000, duration: 0.15 }
        };
    }
    
    playSound(type) {
        if (!this.enabled || !this.audioContext) return;
        
        const config = this.soundConfigs[type];
        if (!config) return;
        
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(config.frequency, this.audioContext.currentTime);
        oscillator.type = type === 'error' ? 'sawtooth' : 'sine';
        
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + config.duration);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + config.duration);
    }
    
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }
}

// 粒子效果系统
class ParticleSystem {
    constructor() {
        this.particles = [];
        this.container = null;
        this.initialize();
    }
    
    initialize() {
        this.container = document.createElement('div');
        this.container.className = 'particle-container';
        document.body.appendChild(this.container);
    }
    
    createParticles(x, y, count = 8) {
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const angle = (Math.PI * 2 * i) / count;
            const velocity = 50 + Math.random() * 30;
            const lifetime = 800 + Math.random() * 400;
            
            particle.style.left = x + 'px';
            particle.style.top = y + 'px';
            particle.style.background = `hsl(${Math.random() * 60 + 300}, 70%, 60%)`;
            
            this.container.appendChild(particle);
            
            const startTime = Date.now();
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = elapsed / lifetime;
                
                if (progress >= 1) {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                    return;
                }
                
                const currentX = x + Math.cos(angle) * velocity * progress;
                const currentY = y + Math.sin(angle) * velocity * progress - (progress * progress * 30);
                const opacity = 1 - progress;
                
                particle.style.left = currentX + 'px';
                particle.style.top = currentY + 'px';
                particle.style.opacity = opacity;
                
                requestAnimationFrame(animate);
            };
            
            requestAnimationFrame(animate);
        }
    }
}

// 游戏类定义
class VocabularyGame {
    constructor() {
        this.gameState = {
            score: 0,
            matches: 0,
            totalPairs: 18,
            timeElapsed: 0,
            isPlaying: false,
            isPaused: false,
            selectedCell: null,
            gridData: [],
            combo: 0,
            consecutiveMatches: 0,
            correctPairs: 0,
            wrongAttempts: 0
        };
        
        this.elements = {
            gameGrid: document.getElementById('gameGrid'),
            scoreElement: document.getElementById('score'),
            timerElement: document.getElementById('timer'),
            accuracyElement: document.getElementById('accuracy'),
            startBtn: document.getElementById('startBtn'),
            restartBtn: document.getElementById('restartBtn'),
            hintBtn: document.getElementById('hintBtn'),
            soundBtn: document.getElementById('soundBtn'),
            statsBtn: document.getElementById('statsBtn'),
            gameMessage: document.getElementById('gameMessage'),
            messageTitle: document.getElementById('messageTitle'),
            messageText: document.getElementById('messageText'),
            newGameBtn: document.getElementById('newGameBtn'),
            selectionIndicator: document.getElementById('selectionIndicator'),
            selectedWord: document.getElementById('selectedWord')
        };
        
        this.timer = null;
        this.hintUsed = false;
        this.soundSystem = new SoundSystem();
        this.particleSystem = new ParticleSystem();
        
        this.initializeEventListeners();
        this.renderInitialState();
    }
    
    // 初始化事件监听器
    initializeEventListeners() {
        this.elements.startBtn.addEventListener('click', () => this.startGame());
        this.elements.restartBtn.addEventListener('click', () => this.restartGame());
        this.elements.hintBtn.addEventListener('click', () => this.showHint());
        this.elements.soundBtn.addEventListener('click', () => this.toggleSound());
        this.elements.statsBtn.addEventListener('click', () => this.showStats());
        this.elements.newGameBtn.addEventListener('click', () => this.startNewGame());
        
        // 点击消息窗口外部关闭
        this.elements.gameMessage.addEventListener('click', (e) => {
            if (e.target === this.elements.gameMessage) {
                this.hideMessage();
            }
        });
    }
    
    // 渲染初始状态
    renderInitialState() {
        this.updateUI();
        this.renderGrid();
    }
    
    // 开始游戏
    startGame() {
        this.gameState.isPlaying = true;
        this.gameState.timeElapsed = 0;
        this.gameState.score = 0;
        this.gameState.matches = 0;
        this.gameState.correctPairs = 0;
        this.gameState.wrongAttempts = 0;
        this.gameState.selectedCell = null;
        this.hintUsed = false;
        
        this.generateNewGrid();
        this.startTimer();
        this.updateUI();
        this.hideSelectionIndicator();
        
        this.elements.startBtn.textContent = '游戏中...';
        this.elements.startBtn.disabled = true;
    }
    
    // 重新开始游戏
    restartGame() {
        this.stopTimer();
        this.elements.startBtn.textContent = '开始游戏';
        this.elements.startBtn.disabled = false;
        this.gameState.isPlaying = false;
        this.hideMessage();
        this.renderInitialState();
    }
    
    // 开始新游戏
    startNewGame() {
        this.hideMessage();
        this.startGame();
    }
    
    // 生成新的游戏网格
    generateNewGrid() {
        this.gameState.gridData = generateGameGrid();
        this.renderGrid();
    }
    
    // 渲染游戏网格
    renderGrid() {
        this.elements.gameGrid.innerHTML = '';
        
        this.gameState.gridData.forEach((cellData, index) => {
            const cell = document.createElement('div');
            cell.className = `game-cell ${cellData.type} new`;
            
            // 创建主要内容
            const mainContent = document.createElement('div');
            mainContent.className = 'cell-main';
            mainContent.textContent = cellData.content;
            cell.appendChild(mainContent);
            
            // 如果是英文单词且有音标，添加音标显示
            if (cellData.type === 'english' && cellData.phonetic) {
                const phoneticElement = document.createElement('div');
                phoneticElement.className = 'cell-phonetic';
                phoneticElement.textContent = cellData.phonetic;
                cell.appendChild(phoneticElement);
            }
            
            cell.dataset.id = cellData.id;
            cell.dataset.match = cellData.match;
            cell.dataset.type = cellData.type;
            
            if (cellData.matched) {
                cell.classList.add('matched');
            }
            
            cell.addEventListener('click', () => this.handleCellClick(cell, cellData));
            
            this.elements.gameGrid.appendChild(cell);
        });
    }
    
    // 处理格子点击
    handleCellClick(cellElement, cellData) {
        if (!this.gameState.isPlaying || cellData.matched || cellElement.classList.contains('matched')) {
            return;
        }
        
        // 如果点击的是已选中的格子，取消选中
        if (this.gameState.selectedCell && this.gameState.selectedCell.id === cellData.id) {
            this.clearSelection();
            return;
        }
        
        // 如果没有选中的格子，选中当前格子
        if (!this.gameState.selectedCell) {
            this.selectCell(cellElement, cellData);
            return;
        }
        
        // 如果已有选中的格子，检查是否匹配
        if (this.gameState.selectedCell.match === cellData.id) {
            this.handleMatch(cellElement, cellData);
        } else {
            this.handleMismatch(cellElement, cellData);
        }
    }
    
    // 选中格子
    selectCell(cellElement, cellData) {
        this.gameState.selectedCell = cellData;
        cellElement.classList.add('selected');
        this.soundSystem.playSound('select');
        this.showSelectionIndicator(cellData.content);
    }
    
    // 清除选中状态
    clearSelection() {
        if (this.gameState.selectedCell) {
            const selectedElement = document.querySelector(`[data-id="${this.gameState.selectedCell.id}"]`);
            if (selectedElement) {
                selectedElement.classList.remove('selected');
            }
        }
        this.gameState.selectedCell = null;
        this.hideSelectionIndicator();
    }
    
    // 处理匹配成功
    handleMatch(cellElement, cellData) {
        const selectedElement = document.querySelector(`[data-id="${this.gameState.selectedCell.id}"]`);
        
        // 播放匹配音效
        this.soundSystem.playSound('match');
        
        // 添加匹配动画
        cellElement.classList.add('correct');
        selectedElement.classList.add('correct');
        
        // 创建粒子效果
        const cellRect = cellElement.getBoundingClientRect();
        const selectedRect = selectedElement.getBoundingClientRect();
        this.particleSystem.createParticles(
            cellRect.left + cellRect.width / 2,
            cellRect.top + cellRect.height / 2,
            6
        );
        this.particleSystem.createParticles(
            selectedRect.left + selectedRect.width / 2,
            selectedRect.top + selectedRect.height / 2,
            6
        );
        
        // 更新连击系统
        this.gameState.consecutiveMatches++;
        if (this.gameState.consecutiveMatches >= 3) {
            this.showComboEffect();
        }
        
        setTimeout(() => {
            // 标记为匹配
            cellElement.classList.add('matched', 'disappear');
            selectedElement.classList.add('matched', 'disappear');
            cellElement.classList.remove('selected', 'correct');
            selectedElement.classList.remove('selected', 'correct');
            
            // 更新数据
            const selectedIndex = this.gameState.gridData.findIndex(cell => cell.id === this.gameState.selectedCell.id);
            const currentIndex = this.gameState.gridData.findIndex(cell => cell.id === cellData.id);
            
            if (selectedIndex !== -1) this.gameState.gridData[selectedIndex].matched = true;
            if (currentIndex !== -1) this.gameState.gridData[currentIndex].matched = true;
            
            this.gameState.matches++;
            this.gameState.correctPairs++;
            this.gameState.score += this.calculateScore();
            
            // 记录学习结果
            const englishWord = cellData.type === 'english' ? cellData.content : this.gameState.selectedCell.content;
            recordPairResult(englishWord, true);
            
            this.gameState.selectedCell = null;
            this.hideSelectionIndicator();
            this.updateUI();
            
            // 检查是否完成游戏
            if (this.gameState.matches >= this.gameState.totalPairs) {
                this.handleGameComplete();
            }
        }, 600);
    }
    
    // 处理匹配失败
    handleMismatch(cellElement, cellData) {
        const selectedElement = document.querySelector(`[data-id="${this.gameState.selectedCell.id}"]`);
        
        // 播放错误音效
        this.soundSystem.playSound('error');
        
        // 重置连击计数
        this.gameState.consecutiveMatches = 0;
        this.gameState.wrongAttempts++;
        
        // 记录学习结果 - 两个单词都记录为错误
        const englishWord1 = cellData.type === 'english' ? cellData.content : cellData.wordData?.english;
        const englishWord2 = this.gameState.selectedCell.type === 'english' ? this.gameState.selectedCell.content : this.gameState.selectedCell.wordData?.english;
        
        if (englishWord1) recordPairResult(englishWord1, false);
        if (englishWord2 && englishWord2 !== englishWord1) recordPairResult(englishWord2, false);
        
        // 显示错误效果
        cellElement.classList.add('error');
        selectedElement.classList.add('error');
        
        // 清除选中状态
        setTimeout(() => {
            cellElement.classList.remove('error', 'selected');
            selectedElement.classList.remove('error', 'selected');
            this.gameState.selectedCell = null;
            this.hideSelectionIndicator();
        }, 500);
    }
    
    // 计算得分
    calculateScore() {
        let baseScore = 100;
        let timeBonus = Math.max(0, 300 - this.gameState.timeElapsed) / 10;
        let hintPenalty = this.hintUsed ? 20 : 0;
        let comboBonus = this.gameState.consecutiveMatches >= 3 ? this.gameState.consecutiveMatches * 25 : 0;
        let accuracyBonus = this.gameState.wrongAttempts === 0 ? 50 : Math.max(0, 50 - this.gameState.wrongAttempts * 5);
        
        return Math.round(baseScore + timeBonus + comboBonus + accuracyBonus - hintPenalty);
    }
    
    // 显示连击效果
    showComboEffect() {
        const comboElement = document.createElement('div');
        comboElement.className = 'combo-indicator';
        comboElement.textContent = `${this.gameState.consecutiveMatches}连击！+${this.gameState.consecutiveMatches * 25}分`;
        
        document.body.appendChild(comboElement);
        
        setTimeout(() => {
            if (comboElement.parentNode) {
                comboElement.parentNode.removeChild(comboElement);
            }
        }, 1500);
    }
    
    // 游戏完成处理
    handleGameComplete() {
        this.stopTimer();
        this.soundSystem.playSound('levelComplete');
        
        // 完成学习记录
        finishGameTracking();
        
        let completionTime = this.formatTime(this.gameState.timeElapsed);
        let finalScore = this.gameState.score;
        let accuracy = ((this.gameState.correctPairs / (this.gameState.correctPairs + this.gameState.wrongAttempts)) * 100).toFixed(1);
        
        // 重置连击计数
        this.gameState.consecutiveMatches = 0;
        
        // 获取学习统计
        const learningStats = learningTracker.getLearningStats();
        
        setTimeout(() => {
            this.elements.messageTitle.textContent = '游戏完成！';
            this.elements.messageText.innerHTML = `
                <strong>完成时间:</strong> ${completionTime}<br>
                <strong>最终得分:</strong> ${finalScore}<br>
                <strong>本轮正确率:</strong> ${accuracy}%<br>
                <strong>错误次数:</strong> ${this.gameState.wrongAttempts}<br>
                <hr style="margin: 15px 0;">
                <strong>总体学习进度:</strong><br>
                已学习词汇: ${learningStats.totalWords}<br>
                掌握词汇: ${learningStats.masteredWords}<br>
                需练习: ${learningStats.strugglingWords}
            `;
            this.elements.newGameBtn.style.display = 'block';
            this.showMessage();
        }, 1000);
    }
    
    // 显示提示
    showHint() {
        if (!this.gameState.isPlaying || this.hintUsed) {
            return;
        }
        
        const hint = getHint(this.gameState.gridData);
        if (hint) {
            this.hintUsed = true;
            this.elements.hintBtn.disabled = true;
            this.elements.hintBtn.textContent = '已使用';
            this.soundSystem.playSound('hint');
            
            // 高亮提示的两个格子
            hint.forEach(cellId => {
                const cellElement = document.querySelector(`[data-id="${cellId}"]`);
                if (cellElement) {
                    cellElement.classList.add('hint');
                }
            });
            
            setTimeout(() => {
                hint.forEach(cellId => {
                    const cellElement = document.querySelector(`[data-id="${cellId}"]`);
                    if (cellElement) {
                        cellElement.classList.remove('hint');
                    }
                });
            }, 3000);
        }
    }
    
    // 开始计时器
    startTimer() {
        this.timer = setInterval(() => {
            this.gameState.timeElapsed++;
            this.updateTimer();
        }, 1000);
    }
    
    // 停止计时器
    stopTimer() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
    
    // 更新计时器显示
    updateTimer() {
        this.elements.timerElement.textContent = this.formatTime(this.gameState.timeElapsed);
    }
    
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    // 更新UI
    updateUI() {
        const previousScore = parseInt(this.elements.scoreElement.textContent);
        const currentScore = this.gameState.score;
        
        this.elements.scoreElement.textContent = currentScore;
        this.elements.timerElement.textContent = this.formatTime(this.gameState.timeElapsed);
        
        // 计算并显示当前正确率
        const totalAttempts = this.gameState.correctPairs + this.gameState.wrongAttempts;
        const accuracy = totalAttempts > 0 ? ((this.gameState.correctPairs / totalAttempts) * 100).toFixed(1) : 100;
        this.elements.accuracyElement.textContent = `${accuracy}%`;
        
        // 如果分数有变化，添加动画效果
        if (currentScore > previousScore) {
            this.elements.scoreElement.classList.add('updated');
            setTimeout(() => {
                this.elements.scoreElement.classList.remove('updated');
            }, 500);
        }
        
        // 重置提示按钮
        if (!this.hintUsed) {
            this.elements.hintBtn.disabled = false;
            this.elements.hintBtn.textContent = '提示';
        }
    }
    
    // 显示选择指示器
    showSelectionIndicator(word) {
        this.elements.selectedWord.textContent = word;
        this.elements.selectionIndicator.classList.add('show');
    }
    
    // 隐藏选择指示器
    hideSelectionIndicator() {
        this.elements.selectionIndicator.classList.remove('show');
    }
    
    // 显示消息窗口
    showMessage() {
        this.elements.gameMessage.style.display = 'flex';
    }
    
    // 隐藏消息窗口
    hideMessage() {
        this.elements.gameMessage.style.display = 'none';
        this.elements.newGameBtn.style.display = 'none';
    }
    
    // 切换音效
    toggleSound() {
        const enabled = this.soundSystem.toggle();
        this.elements.soundBtn.textContent = enabled ? '🔊 音效' : '🔇 静音';
        this.elements.soundBtn.classList.toggle('disabled', !enabled);
    }
    
    // 显示学习统计
    showStats() {
        const stats = learningTracker.getLearningStats();
        
        this.elements.messageTitle.textContent = '学习统计';
        this.elements.messageText.innerHTML = `
            <div style="text-align: left;">
                <strong>📚 总体进度:</strong><br>
                已学习词汇: ${stats.totalWords} 个<br>
                掌握词汇: ${stats.masteredWords} 个<br>
                需要练习: ${stats.strugglingWords} 个<br><br>
                
                <strong>🎯 掌握度:</strong><br>
                总体掌握率: ${stats.accuracy}%<br><br>
                
                <div style="font-size: 0.9em; color: #666;">
                💡 游戏会根据你的学习情况智能调整词汇出现频率：<br>
                • 掌握较好的词汇出现频率会降低<br>
                • 需要练习的词汇会更频繁出现<br>
                • 持续游戏以获得最佳学习效果
                </div>
            </div>
        `;
        this.elements.newGameBtn.style.display = 'none';
        this.showMessage();
    }
}

// 游戏工具函数
class GameUtils {
    // 保存游戏状态到本地存储
    static saveGameState(gameState) {
        localStorage.setItem('vocabularyGameState', JSON.stringify(gameState));
    }
    
    // 从本地存储加载游戏状态
    static loadGameState() {
        const saved = localStorage.getItem('vocabularyGameState');
        return saved ? JSON.parse(saved) : null;
    }
    
    // 清除保存的游戏状态
    static clearSavedState() {
        localStorage.removeItem('vocabularyGameState');
    }
    
    // 获取最高分
    static getHighScore() {
        return parseInt(localStorage.getItem('vocabularyGameHighScore') || '0');
    }
    
    // 保存最高分
    static saveHighScore(score) {
        const currentHigh = GameUtils.getHighScore();
        if (score > currentHigh) {
            localStorage.setItem('vocabularyGameHighScore', score.toString());
            return true;
        }
        return false;
    }
}

// 键盘控制
class KeyboardController {
    constructor(game) {
        this.game = game;
        this.initializeKeyboardEvents();
    }
    
    initializeKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            if (!this.game.gameState.isPlaying) return;
            
            switch(e.key) {
                case 'Escape':
                    this.game.clearSelection();
                    break;
                case 'h':
                case 'H':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.game.showHint();
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.game.restartGame();
                    }
                    break;
            }
        });
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new VocabularyGame();
    const keyboardController = new KeyboardController(game);
    
    // 全局游戏对象，便于调试
    window.vocabularyGame = game;
    
    console.log('英语词汇消消乐游戏已启动！');
    console.log('快捷键: Esc-取消选择, Ctrl+H-提示, Ctrl+R-重新开始');
});