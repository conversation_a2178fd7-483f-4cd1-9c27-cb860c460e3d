<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语词汇消消乐</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- 游戏头部 -->
        <header class="game-header">
            <h1 class="game-title">🎯 智能英语词汇消消乐</h1>
            <div class="game-stats">
                <div class="stat-item">
                    <span class="stat-label">分数</span>
                    <span class="stat-value" id="score">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">时间</span>
                    <span class="stat-value" id="timer">00:00</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">正确率</span>
                    <span class="stat-value" id="accuracy">100%</span>
                </div>
            </div>
        </header>

        <!-- 游戏说明 -->
        <div class="game-instructions">
            <p>📚 智能学习系统会根据你的掌握情况调整词汇出现频率，越练越精准！</p>
        </div>

        <!-- 游戏网格 -->
        <div class="game-grid" id="gameGrid">
            <!-- 6x6 = 36个格子将通过JavaScript动态生成 -->
        </div>

        <!-- 游戏控制 -->
        <div class="game-controls">
            <button class="btn btn-primary" id="startBtn">开始游戏</button>
            <button class="btn btn-secondary" id="restartBtn">重新开始</button>
            <button class="btn btn-hint" id="hintBtn">💡 提示</button>
            <button class="btn btn-info" id="statsBtn">📊 学习统计</button>
            <button class="btn btn-secondary" id="soundBtn">🔊 音效</button>
        </div>

        <!-- 游戏状态提示 -->
        <div class="game-message" id="gameMessage" style="display: none;">
            <div class="message-content">
                <h3 id="messageTitle"></h3>
                <p id="messageText"></p>
                <button class="btn btn-primary" id="newGameBtn" style="display: none;">开始新游戏</button>
            </div>
        </div>
    </div>

    <!-- 选中状态指示器 -->
    <div class="selection-indicator" id="selectionIndicator">
        <span>已选择: </span>
        <span id="selectedWord"></span>
    </div>

    <script src="vocabulary.js"></script>
    <script src="game.js"></script>
</body>
</html>