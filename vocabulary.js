// 统一英语词汇数据池
const allVocabulary = [
    // 基础高频词汇
    { english: "abandon", chinese: "遗弃；放弃", phonetic: "/əˈbændən/" },
    { english: "ability", chinese: "能力，才能", phonetic: "/əˈbɪləti/" },
    { english: "able", chinese: "能干的", phonetic: "/ˈeɪbl/" },
    { english: "about", chinese: "大约，关于", phonetic: "/əˈbaʊt/" },
    { english: "above", chinese: "在上面", phonetic: "/əˈbʌv/" },
    { english: "abroad", chinese: "在国外", phonetic: "/əˈbrɔːd/" },
    { english: "absence", chinese: "缺席；缺乏", phonetic: "/ˈæbsəns/" },
    { english: "absent", chinese: "缺席的", phonetic: "/ˈæbsənt/" },
    { english: "absolute", chinese: "绝对的", phonetic: "/ˈæbsəluːt/" },
    { english: "absorb", chinese: "吸收；理解", phonetic: "/əbˈzɔːb/" },
    { english: "abstract", chinese: "抽象的；摘要", phonetic: "/ˈæbstrækt/" },
    { english: "academic", chinese: "学术的", phonetic: "/ækəˈdemɪk/" },
    { english: "accept", chinese: "接受；承认", phonetic: "/əkˈsept/" },
    { english: "access", chinese: "通路，访问", phonetic: "/ˈækses/" },
    { english: "accident", chinese: "事故；意外", phonetic: "/ˈæksɪdənt/" },
    { english: "accompany", chinese: "陪伴，伴随", phonetic: "/əˈkʌmpəni/" },
    { english: "accomplish", chinese: "完成；实现", phonetic: "/əˈkɑːmplɪʃ/" },
    { english: "account", chinese: "账户；解释", phonetic: "/əˈkaʊnt/" },
    { english: "accurate", chinese: "精确的", phonetic: "/ˈækjərət/" },
    { english: "achieve", chinese: "取得；实现", phonetic: "/əˈtʃiːv/" },
    { english: "acquire", chinese: "获得；学到", phonetic: "/əˈkwaɪər/" },
    { english: "action", chinese: "行动；活动", phonetic: "/ˈækʃən/" },
    { english: "active", chinese: "积极的；活跃的", phonetic: "/ˈæktɪv/" },
    { english: "activity", chinese: "活动；行动", phonetic: "/ækˈtɪvəti/" },
    { english: "actual", chinese: "真实的，实际的", phonetic: "/ˈæktʃuəl/" },
    { english: "adapt", chinese: "使适应；改编", phonetic: "/əˈdæpt/" },
    { english: "add", chinese: "增加，添加", phonetic: "/æd/" },
    { english: "addition", chinese: "添加；加法", phonetic: "/əˈdɪʃən/" },
    { english: "adequate", chinese: "充足的；适当的", phonetic: "/ˈædɪkwət/" },
    { english: "adjust", chinese: "调整，校准", phonetic: "/əˈdʒʌst/" },
    
    // 四级核心词汇
    { english: "abnormal", chinese: "反常的，不规则的", phonetic: "/æbˈnɔːrml/" },
    { english: "aboard", chinese: "在船上；在飞机上", phonetic: "/əˈbɔːrd/" },
    { english: "abolish", chinese: "废除，废止", phonetic: "/əˈbɑːlɪʃ/" },
    { english: "abrupt", chinese: "突然的；唐突的", phonetic: "/əˈbrʌpt/" },
    { english: "absolutely", chinese: "绝对地；完全地", phonetic: "/ˈæbsəluːtli/" },
    { english: "absurd", chinese: "荒谬的；可笑的", phonetic: "/əbˈsɜːrd/" },
    { english: "abundance", chinese: "充裕，丰富", phonetic: "/əˈbʌndəns/" },
    { english: "abundant", chinese: "丰富的；充裕的", phonetic: "/əˈbʌndənt/" },
    { english: "abuse", chinese: "滥用；虐待", phonetic: "/əˈbjuːs/" },
    { english: "academy", chinese: "学院；研究院", phonetic: "/əˈkædəmi/" },
    { english: "accelerate", chinese: "使加快；加速", phonetic: "/ækˈseləreɪt/" },
    { english: "accent", chinese: "口音；重音", phonetic: "/ˈæksənt/" },
    { english: "acceptable", chinese: "可接受的", phonetic: "/əkˈseptəbl/" },
    { english: "acceptance", chinese: "接纳；赞同", phonetic: "/əkˈseptəns/" },
    { english: "accessory", chinese: "配件；附件", phonetic: "/əkˈsesəri/" },
    { english: "accidental", chinese: "意外的；偶然的", phonetic: "/æksəˈdentl/" },
    { english: "accommodate", chinese: "容纳；使适应", phonetic: "/əˈkɑːmədeɪt/" },
    { english: "accommodation", chinese: "住处，膳宿", phonetic: "/əkɑːməˈdeɪʃən/" },
    { english: "accord", chinese: "符合；一致", phonetic: "/əˈkɔːrd/" },
    { english: "accordance", chinese: "按照，依据", phonetic: "/əˈkɔːrdns/" },
    { english: "accordingly", chinese: "因此，于是", phonetic: "/əˈkɔːrdɪŋli/" },
    { english: "accumulate", chinese: "累积；积聚", phonetic: "/əˈkjuːmjəleɪt/" },
    { english: "accuracy", chinese: "精确度，准确性", phonetic: "/ˈækjərəsi/" },
    { english: "accuse", chinese: "控告，指控", phonetic: "/əˈkjuːz/" },
    
    // 六级进阶词汇
    { english: "additional", chinese: "附加的，额外的", phonetic: "/əˈdɪʃənl/" },
    { english: "address", chinese: "地址，致辞", phonetic: "/əˈdres/" },
    { english: "adhere", chinese: "坚持；依附", phonetic: "/ədˈhɪr/" },
    { english: "adjacent", chinese: "邻近的，毗连的", phonetic: "/əˈdʒeɪsnt/" },
    { english: "adjective", chinese: "形容词", phonetic: "/ˈædʒɪktɪv/" },
    { english: "administer", chinese: "管理；执行", phonetic: "/ədˈmɪnɪstər/" },
    { english: "administration", chinese: "管理；行政", phonetic: "/ədmɪnɪˈstreɪʃən/" },
    { english: "admire", chinese: "钦佩；赞美", phonetic: "/ədˈmaɪər/" },
    { english: "admission", chinese: "承认；入场费", phonetic: "/ədˈmɪʃən/" },
    { english: "adolescent", chinese: "青春期的；青少年", phonetic: "/ædəˈlesnt/" },
    { english: "advanced", chinese: "先进的；高级的", phonetic: "/ədˈvænst/" },
    { english: "advent", chinese: "到来；出现", phonetic: "/ˈædvent/" },
    { english: "adverse", chinese: "不利的；相反的", phonetic: "/ədˈvɜːrs/" },
    { english: "advertisement", chinese: "广告，宣传", phonetic: "/ædvərˈtaɪzmənt/" },
    { english: "advisable", chinese: "明智的，可取的", phonetic: "/ədˈvaɪzəbl/" },
    { english: "advocate", chinese: "提倡，拥护", phonetic: "/ˈædvəkeɪt/" },
    { english: "aesthetic", chinese: "美的；审美的", phonetic: "/esˈθetɪk/" },
    { english: "affiliate", chinese: "附属，隶属", phonetic: "/əˈfɪlieɪt/" },
    { english: "affirm", chinese: "肯定；断言", phonetic: "/əˈfɜːrm/" },
    { english: "afflict", chinese: "折磨；使痛苦", phonetic: "/əˈflɪkt/" },
    
    // 高级词汇
    { english: "agency", chinese: "代理，中介", phonetic: "/ˈeɪdʒənsi/" },
    { english: "agenda", chinese: "议程；日程表", phonetic: "/əˈdʒendə/" },
    { english: "aggravate", chinese: "加重；使恶化", phonetic: "/ˈæɡrəveɪt/" },
    { english: "aggressive", chinese: "侵略性的；好斗的", phonetic: "/əˈɡresɪv/" },
    { english: "agitation", chinese: "激动；搅动", phonetic: "/ædʒɪˈteɪʃən/" },
    { english: "agony", chinese: "苦恼；极大的痛苦", phonetic: "/ˈæɡəni/" },
    { english: "aircraft", chinese: "飞机，航空器", phonetic: "/ˈerkræft/" },
    { english: "airline", chinese: "航空公司；航线", phonetic: "/ˈerlaɪn/" },
    { english: "airplane", chinese: "飞机", phonetic: "/ˈerpleɪn/" },
    { english: "airport", chinese: "机场；航空站", phonetic: "/ˈerpɔːrt/" },
    { english: "alarm", chinese: "闹钟；警报", phonetic: "/əˈlɑːrm/" },
    { english: "alcohol", chinese: "酒精，乙醇", phonetic: "/ˈælkəhɔːl/" },
    { english: "alert", chinese: "提防的，警惕的", phonetic: "/əˈlɜːrt/" },
    { english: "alien", chinese: "外国的；外星人", phonetic: "/ˈeɪliən/" },
    { english: "alike", chinese: "相似的；相像的", phonetic: "/əˈlaɪk/" },
    { english: "alive", chinese: "活着的；活泼的", phonetic: "/əˈlaɪv/" },
    { english: "allege", chinese: "宣称，断言", phonetic: "/əˈledʒ/" },
    { english: "alleviate", chinese: "减轻，缓和", phonetic: "/əˈliːvieɪt/" },
    { english: "allocate", chinese: "分配；分派", phonetic: "/ˈæləkeɪt/" },
    { english: "alternate", chinese: "交替的；备选的", phonetic: "/ˈɔːltərneɪt/" },
    { english: "ambiguous", chinese: "模糊的；含糊的", phonetic: "/æmˈbɪɡjuəs/" },
    { english: "anticipate", chinese: "预期；期望", phonetic: "/ænˈtɪsɪpeɪt/" },
    { english: "apparatus", chinese: "装置；器械", phonetic: "/æpəˈreɪtəs/" },
    { english: "arbitrary", chinese: "任意的；专制的", phonetic: "/ˈɑːrbɪtreri/" },
    { english: "architecture", chinese: "建筑学；建筑风格", phonetic: "/ˈɑːrkɪtektʃər/" },
    { english: "artificial", chinese: "人工的；人造的", phonetic: "/ɑːrtɪˈfɪʃəl/" },
    { english: "assemble", chinese: "集合；装配", phonetic: "/əˈsembl/" },
    { english: "assess", chinese: "评估；评价", phonetic: "/əˈses/" },
    { english: "assign", chinese: "分配；指派", phonetic: "/əˈsaɪn/" },
    { english: "assistance", chinese: "援助；帮助", phonetic: "/əˈsɪstəns/" },
    { english: "associate", chinese: "联合；关联", phonetic: "/əˈsoʊsieɪt/" },
    { english: "assumption", chinese: "假设；承担", phonetic: "/əˈsʌmpʃən/" },
    { english: "atmosphere", chinese: "大气；氛围", phonetic: "/ˈætməsfɪr/" },
    { english: "attribute", chinese: "属性；归因于", phonetic: "/əˈtrɪbjuːt/" },
    { english: "authentic", chinese: "真实的；可信的", phonetic: "/ɔːˈθentɪk/" },
    { english: "authority", chinese: "权威；当局", phonetic: "/əˈθɔːrəti/" },
    { english: "automatic", chinese: "自动的；机械的", phonetic: "/ɔːtəˈmætɪk/" },
    { english: "available", chinese: "可获得的；可用的", phonetic: "/əˈveɪləbl/" },
    { english: "beneficial", chinese: "有益的；有利的", phonetic: "/benɪˈfɪʃəl/" },
    { english: "biological", chinese: "生物的；生物学的", phonetic: "/baɪəˈlɑːdʒɪkəl/" },
    { english: "bizarre", chinese: "奇异的；怪诞的", phonetic: "/bɪˈzɑːr/" },
    
    // 新增词汇 (A字母续)
    { english: "alliance", chinese: "联盟，联合", phonetic: "/əˈlaɪəns/" },
    { english: "allocate", chinese: "分配；拨出", phonetic: "/ˈæləkeɪt/" },
    { english: "allow", chinese: "允许；给予", phonetic: "/əˈlaʊ/" },
    { english: "allowance", chinese: "津贴，零用钱", phonetic: "/əˈlaʊəns/" },
    { english: "alloy", chinese: "合金", phonetic: "/ˈælɔɪ/" },
    { english: "ally", chinese: "同盟国；伙伴", phonetic: "/əˈlaɪ/" },
    { english: "almost", chinese: "差不多，几乎", phonetic: "/ˈɔːlmoʊst/" },
    { english: "alone", chinese: "独自的；单独的", phonetic: "/əˈloʊn/" },
    { english: "along", chinese: "沿着；一起", phonetic: "/əˈlɔːŋ/" },
    { english: "alongside", chinese: "在……旁边", phonetic: "/əˌlɔːŋˈsaɪd/" },
    { english: "aloud", chinese: "大声地；出声地", phonetic: "/əˈlaʊd/" },
    { english: "alphabet", chinese: "字母表", phonetic: "/ˈælfəbet/" },
    { english: "already", chinese: "已经，早已", phonetic: "/ɔːlˈredi/" },
    { english: "also", chinese: "也；而且", phonetic: "/ˈɔːlsoʊ/" },
    { english: "alter", chinese: "改变，更改", phonetic: "/ˈɔːltər/" },
    { english: "alternate", chinese: "交替的", phonetic: "/ˈɔːltərnət/" },
    { english: "alternative", chinese: "供选择的；选择性的", phonetic: "/ɔːlˈtɜːrnətɪv/" },
    { english: "although", chinese: "尽管，虽然", phonetic: "/ɔːlˈðoʊ/" },
    { english: "altitude", chinese: "高度；海拔", phonetic: "/ˈæltɪtuːd/" },
    { english: "altogether", chinese: "完全地；总共", phonetic: "/ˌɔːltəˈɡeðər/" },
    { english: "aluminium", chinese: "铝", phonetic: "/ˌæljəˈmɪniəm/" },
    { english: "always", chinese: "永远，一直", phonetic: "/ˈɔːlweɪz/" },
    { english: "amateur", chinese: "业余爱好者", phonetic: "/ˈæmətər/" },
    { english: "amaze", chinese: "使吃惊", phonetic: "/əˈmeɪz/" },
    { english: "ambassador", chinese: "大使；代表", phonetic: "/æmˈbæsədər/" },
    { english: "ambiguous", chinese: "模糊不清的", phonetic: "/æmˈbɪɡjuəs/" },
    { english: "ambition", chinese: "野心，雄心", phonetic: "/æmˈbɪʃən/" },
    { english: "ambitious", chinese: "野心勃勃的", phonetic: "/æmˈbɪʃəs/" },
    { english: "ambulance", chinese: "救护车", phonetic: "/ˈæmbjələns/" },
    { english: "amend", chinese: "修改；改善", phonetic: "/əˈmend/" },
    { english: "amends", chinese: "赔偿；赔罪", phonetic: "/əˈmendz/" },
    { english: "America", chinese: "美洲；美国", phonetic: "/əˈmerɪkə/" },
    { english: "American", chinese: "美国人；美国的", phonetic: "/əˈmerɪkən/" },
    { english: "among", chinese: "在……中间", phonetic: "/əˈmʌŋ/" },
    { english: "amongst", chinese: "在……当中", phonetic: "/əˈmʌŋst/" },
    { english: "amount", chinese: "数量，数额", phonetic: "/əˈmaʊnt/" },
    { english: "ampere", chinese: "安培", phonetic: "/ˈæmpɪr/" },
    { english: "ample", chinese: "丰富的；足够的", phonetic: "/ˈæmpl/" },
    { english: "amplify", chinese: "放大，扩大", phonetic: "/ˈæmplɪfaɪ/" },
    { english: "amuse", chinese: "娱乐；消遣", phonetic: "/əˈmjuːz/" },
    { english: "analogy", chinese: "类比；类推", phonetic: "/əˈnælədʒi/" },
    { english: "analyse", chinese: "分析；分解", phonetic: "/ˈænəlaɪz/" },
    { english: "analysis", chinese: "分析；分解", phonetic: "/əˈnæləsɪs/" },
    { english: "analytic", chinese: "分析的；解析的", phonetic: "/ˌænəˈlɪtɪk/" },
    { english: "analytical", chinese: "分析的；解析的", phonetic: "/ˌænəˈlɪtɪkl/" },
    { english: "ancestor", chinese: "始祖，祖先", phonetic: "/ˈænsestər/" },
    { english: "anchor", chinese: "锚；抛锚停泊", phonetic: "/ˈæŋkər/" },
    { english: "ancient", chinese: "古代的；古老的", phonetic: "/ˈeɪnʃənt/" },
    { english: "angel", chinese: "天使", phonetic: "/ˈeɪndʒəl/" },
    { english: "anger", chinese: "怒，愤怒", phonetic: "/ˈæŋɡər/" },
    { english: "angle", chinese: "角，角度", phonetic: "/ˈæŋɡl/" },
    { english: "angry", chinese: "生气的；愤怒的", phonetic: "/ˈæŋɡri/" },
    { english: "animal", chinese: "动物", phonetic: "/ˈænɪml/" },
    { english: "ankle", chinese: "踝关节", phonetic: "/ˈæŋkl/" },
    { english: "anniversary", chinese: "周年纪念日", phonetic: "/ˌænɪˈvɜːrsəri/" },
    { english: "announce", chinese: "宣布；述说", phonetic: "/əˈnaʊns/" },
    { english: "announcer", chinese: "广播员；宣告者", phonetic: "/əˈnaʊnsər/" },
    { english: "annoy", chinese: "骚扰；惹恼", phonetic: "/əˈnɔɪ/" },
    { english: "annual", chinese: "年度的；每年的", phonetic: "/ˈænjuəl/" },
    { english: "anonymous", chinese: "匿名的，无名的", phonetic: "/əˈnɑːnəməs/" },
    { english: "another", chinese: "又一，另一", phonetic: "/əˈnʌðər/" },
    { english: "answer", chinese: "答案，回答", phonetic: "/ˈænsər/" },
    { english: "ant", chinese: "蚂蚁", phonetic: "/ænt/" },
    { english: "antarctic", chinese: "南极的", phonetic: "/ænˈtɑːrktɪk/" },
    { english: "anticipate", chinese: "预料，预期", phonetic: "/ænˈtɪsɪpeɪt/" },
    { english: "antique", chinese: "古老的；古董", phonetic: "/ænˈtiːk/" },
    { english: "anxiety", chinese: "焦虑；渴望", phonetic: "/æŋˈzaɪəti/" },
    { english: "anxious", chinese: "焦虑的；担忧的", phonetic: "/ˈæŋkʃəs/" },
    { english: "anybody", chinese: "任何人", phonetic: "/ˈenibɑːdi/" },
    { english: "anyhow", chinese: "总之；无论如何", phonetic: "/ˈenihaʊ/" },
    { english: "anyone", chinese: "任何人", phonetic: "/ˈeniwʌn/" },
    { english: "anything", chinese: "任何东西", phonetic: "/ˈeniθɪŋ/" },
    { english: "anyway", chinese: "无论如何", phonetic: "/ˈeniweɪ/" },
    { english: "anywhere", chinese: "在任何地方", phonetic: "/ˈeniwer/" },
    { english: "apart", chinese: "相距；分离着", phonetic: "/əˈpɑːrt/" },
    { english: "apartment", chinese: "公寓；房间", phonetic: "/əˈpɑːrtmənt/" },
    { english: "apologize", chinese: "道歉，谢罪", phonetic: "/əˈpɑːlədʒaɪz/" },
    { english: "apology", chinese: "道歉；谢罪", phonetic: "/əˈpɑːlədʒi/" },
    { english: "apparatus", chinese: "装置，设备", phonetic: "/æpəˈreɪtəs/" },
    { english: "apparent", chinese: "显然的；表面上的", phonetic: "/əˈpærənt/" },
    { english: "appeal", chinese: "请求，呼吁", phonetic: "/əˈpiːl/" },
    { english: "appear", chinese: "出现；显得", phonetic: "/əˈpɪr/" },
    { english: "appearance", chinese: "外貌，外观", phonetic: "/əˈpɪrəns/" },
    { english: "appendix", chinese: "附录；阑尾", phonetic: "/əˈpendɪks/" },
    { english: "appetite", chinese: "食欲；嗜好", phonetic: "/ˈæpɪtaɪt/" },
    { english: "applaud", chinese: "赞同；称赞", phonetic: "/əˈplɔːd/" },
    { english: "apple", chinese: "苹果", phonetic: "/ˈæpl/" },
    { english: "appliance", chinese: "器具；器械", phonetic: "/əˈplaɪəns/" },
    { english: "applicable", chinese: "可适用的", phonetic: "/əˈplɪkəbl/" },
    { english: "application", chinese: "应用；申请", phonetic: "/æplɪˈkeɪʃən/" },
    { english: "apply", chinese: "申请；应用", phonetic: "/əˈplaɪ/" },
    { english: "appoint", chinese: "任命；指定", phonetic: "/əˈpɔɪnt/" },
    { english: "appointment", chinese: "任命；约定", phonetic: "/əˈpɔɪntmənt/" },
    { english: "appraisal", chinese: "评价；估价", phonetic: "/əˈpreɪzl/" },
    { english: "appreciable", chinese: "可感知的", phonetic: "/əˈpriːʃəbl/" },
    { english: "appreciate", chinese: "欣赏；感激", phonetic: "/əˈpriːʃieɪt/" },
    { english: "apprehension", chinese: "理解；恐惧", phonetic: "/æprɪˈhenʃən/" },
    { english: "approach", chinese: "接近，方法", phonetic: "/əˈproʊtʃ/" },
    { english: "appropriate", chinese: "适当的；恰当的", phonetic: "/əˈproʊpriət/" },
    { english: "approval", chinese: "批准；认可", phonetic: "/əˈpruːvl/" },
    { english: "approve", chinese: "批准；赞成", phonetic: "/əˈpruːv/" },
    { english: "approximate", chinese: "近似的，大概的", phonetic: "/əˈprɑːksɪmət/" },
    { english: "approximately", chinese: "大约，近似地", phonetic: "/əˈprɑːksɪmətli/" },
    { english: "April", chinese: "四月", phonetic: "/ˈeɪprəl/" },
    { english: "apt", chinese: "恰当的；有…倾向的", phonetic: "/æpt/" },
    { english: "Arabian", chinese: "阿拉伯的", phonetic: "/əˈreɪbiən/" },
    { english: "arbitrary", chinese: "任意的；武断的", phonetic: "/ˈɑːrbɪtreri/" },
    { english: "arc", chinese: "弧；弧光", phonetic: "/ɑːrk/" },
    { english: "arch", chinese: "弓形，拱形", phonetic: "/ɑːrtʃ/" },
    { english: "architect", chinese: "建筑师", phonetic: "/ˈɑːrkɪtekt/" },
    { english: "architecture", chinese: "建筑学；建筑风格", phonetic: "/ˈɑːrkɪtektʃər/" },
    { english: "arctic", chinese: "北极的；极寒的", phonetic: "/ˈɑːrktɪk/" },
    { english: "area", chinese: "区域，地区", phonetic: "/ˈeriə/" },
    { english: "argue", chinese: "争论，辩论", phonetic: "/ˈɑːrɡjuː/" },
    { english: "argument", chinese: "论证；论据", phonetic: "/ˈɑːrɡjumənt/" },
    { english: "arise", chinese: "出现；上升", phonetic: "/əˈraɪz/" },
    { english: "arithmetic", chinese: "算术，算法", phonetic: "/əˈrɪθmətɪk/" },
    { english: "army", chinese: "陆军，军队", phonetic: "/ˈɑːrmi/" },
    { english: "around", chinese: "周围，四周", phonetic: "/əˈraʊnd/" },
    { english: "arouse", chinese: "引起；唤醒", phonetic: "/əˈraʊz/" },
    { english: "arrange", chinese: "安排；排列", phonetic: "/əˈreɪndʒ/" },
    { english: "arrangement", chinese: "布置；整理", phonetic: "/əˈreɪndʒmənt/" },
    { english: "array", chinese: "数组，阵列", phonetic: "/əˈreɪ/" },
    { english: "arrest", chinese: "逮捕；阻止", phonetic: "/əˈrest/" },
    { english: "arrival", chinese: "到来；到达", phonetic: "/əˈraɪvl/" },
    { english: "arrive", chinese: "到达；成功", phonetic: "/əˈraɪv/" },
    { english: "arrow", chinese: "箭，箭头", phonetic: "/ˈæroʊ/" },
    { english: "article", chinese: "文章；物品", phonetic: "/ˈɑːrtɪkl/" },
    { english: "articulate", chinese: "清晰明白地说", phonetic: "/ɑːrˈtɪkjuleɪt/" },
    { english: "artificial", chinese: "人造的；仿造的", phonetic: "/ɑːrtɪˈfɪʃəl/" },
    { english: "artist", chinese: "艺术家；美术家", phonetic: "/ˈɑːrtɪst/" },
    { english: "artistic", chinese: "艺术的；风雅的", phonetic: "/ɑːrˈtɪstɪk/" },
    { english: "ascend", chinese: "上升；登高", phonetic: "/əˈsend/" },
    { english: "ascertain", chinese: "确定；查明", phonetic: "/æsərˈteɪn/" },
    { english: "ascribe", chinese: "归因于；归咎于", phonetic: "/əˈskraɪb/" },
    { english: "ashamed", chinese: "羞愧的，羞耻的", phonetic: "/əˈʃeɪmd/" },
    { english: "Asia", chinese: "亚洲", phonetic: "/ˈeɪʒə/" },
    { english: "Asian", chinese: "亚洲人；亚洲的", phonetic: "/ˈeɪʒən/" },
    { english: "aside", chinese: "离开，撇开", phonetic: "/əˈsaɪd/" },
    { english: "asleep", chinese: "睡着的；麻木的", phonetic: "/əˈsliːp/" },
    { english: "aspect", chinese: "方面；方向", phonetic: "/ˈæspekt/" },
    { english: "assault", chinese: "攻击；袭击", phonetic: "/əˈsɔːlt/" },
    
    // 新增词汇 (A字母续)
    { english: "assemble", chinese: "集合，聚集；装配", phonetic: "/əˈsembl/" },
    { english: "assembly", chinese: "装配；集会", phonetic: "/əˈsembli/" },
    { english: "assert", chinese: "维护；断言", phonetic: "/əˈsɜːrt/" },
    { english: "assess", chinese: "评定；估价", phonetic: "/əˈses/" },
    { english: "asset", chinese: "资产；优点", phonetic: "/ˈæset/" },
    { english: "assign", chinese: "分配；指派", phonetic: "/əˈsaɪn/" },
    { english: "assignment", chinese: "分配；任务", phonetic: "/əˈsaɪnmənt/" },
    { english: "assimilate", chinese: "吸收；使同化", phonetic: "/əˈsɪməleɪt/" },
    { english: "assist", chinese: "援助，帮助", phonetic: "/əˈsɪst/" },
    { english: "assistant", chinese: "助手，助理", phonetic: "/əˈsɪstənt/" },
    { english: "associate", chinese: "使联合；合作人", phonetic: "/əˈsoʊsieɪt/" },
    { english: "association", chinese: "协会，联盟", phonetic: "/əˌsoʊsiˈeɪʃən/" },
    { english: "assume", chinese: "假定；承担", phonetic: "/əˈsuːm/" },
    { english: "assumption", chinese: "假定；设想", phonetic: "/əˈsʌmpʃən/" },
    { english: "assurance", chinese: "保证，担保", phonetic: "/əˈʃʊrəns/" },
    { english: "assure", chinese: "保证；担保", phonetic: "/əˈʃʊr/" },
    { english: "astonish", chinese: "使惊讶", phonetic: "/əˈstɑːnɪʃ/" },
    { english: "astronaut", chinese: "宇航员", phonetic: "/ˈæstrənɔːt/" },
    { english: "astronomy", chinese: "天文学", phonetic: "/əˈstrɑːnəmi/" },
    { english: "athlete", chinese: "运动员", phonetic: "/ˈæθliːt/" },
    { english: "Atlantic", chinese: "大西洋", phonetic: "/ətˈlæntɪk/" },
    { english: "atmosphere", chinese: "气氛；大气", phonetic: "/ˈætməsfɪr/" },
    { english: "atmospheric", chinese: "大气的", phonetic: "/ˌætməsˈferɪk/" },
    { english: "atom", chinese: "原子", phonetic: "/ˈætəm/" },
    { english: "atomic", chinese: "原子的", phonetic: "/əˈtɑːmɪk/" },
    { english: "attach", chinese: "使依附；贴上", phonetic: "/əˈtætʃ/" },
    { english: "attack", chinese: "攻击；抨击", phonetic: "/əˈtæk/" },
    { english: "attain", chinese: "达到，实现", phonetic: "/əˈteɪn/" },
    { english: "attempt", chinese: "企图，试图", phonetic: "/əˈtempt/" },
    { english: "attend", chinese: "出席；照料", phonetic: "/əˈtend/" },
    { english: "attendance", chinese: "出席；到场", phonetic: "/əˈtendəns/" },
    { english: "attendant", chinese: "服务员；陪从", phonetic: "/əˈtendənt/" },
    { english: "attention", chinese: "注意力；关心", phonetic: "/əˈtenʃən/" },
    { english: "attentive", chinese: "注意的；体贴的", phonetic: "/əˈtentɪv/" },
    { english: "attitude", chinese: "态度；看法", phonetic: "/ˈætɪtuːd/" },
    { english: "attorney", chinese: "律师；代理人", phonetic: "/əˈtɜːrni/" },
    { english: "attract", chinese: "吸引；引起", phonetic: "/əˈtrækt/" },
    { english: "attraction", chinese: "吸引；吸引力", phonetic: "/əˈtrækʃən/" },
    { english: "attractive", chinese: "吸引人的", phonetic: "/əˈtræktɪv/" },
    { english: "attribute", chinese: "属性；归属", phonetic: "/əˈtrɪbjuːt/" },
    { english: "audience", chinese: "观众；听众", phonetic: "/ˈɔːdiəns/" },
    { english: "augment", chinese: "增加；增大", phonetic: "/ɔːɡˈment/" },
    { english: "August", chinese: "八月", phonetic: "/ˈɔːɡəst/" },
    { english: "aunt", chinese: "阿姨；姑妈", phonetic: "/ænt/" },
    { english: "aural", chinese: "听觉的；耳的", phonetic: "/ˈɔːrəl/" },
    { english: "Australia", chinese: "澳大利亚", phonetic: "/ɔːˈstreɪljə/" },
    { english: "Australian", chinese: "澳大利亚人", phonetic: "/ɔːˈstreɪliən/" },
    { english: "authentic", chinese: "真正的，真实的", phonetic: "/ɔːˈθentɪk/" },
    { english: "author", chinese: "作者；作家", phonetic: "/ˈɔːθər/" },
    { english: "authoritative", chinese: "有权威的", phonetic: "/əˈθɔːrəteɪtɪv/" },
    { english: "authority", chinese: "权威；权力", phonetic: "/əˈθɔːrəti/" },
    { english: "authorize", chinese: "批准，认可", phonetic: "/ˈɔːθəraɪz/" },
    { english: "auto", chinese: "汽车", phonetic: "/ˈɔːtoʊ/" },
    { english: "automatic", chinese: "自动的", phonetic: "/ɔːtəˈmætɪk/" },
    { english: "automation", chinese: "自动化", phonetic: "/ɔːtəˈmeɪʃən/" },
    { english: "automobile", chinese: "汽车", phonetic: "/ɔːtəməˈbiːl/" },
    { english: "autonomy", chinese: "自治，自治权", phonetic: "/ɔːˈtɑːnəmi/" },
    { english: "autumn", chinese: "秋天；成熟期", phonetic: "/ˈɔːtəm/" },
    { english: "auxiliary", chinese: "辅助的；副的", phonetic: "/ɔːɡˈzɪliəri/" },
    { english: "avail", chinese: "有助于；效用", phonetic: "/əˈveɪl/" },
    { english: "available", chinese: "可获得的", phonetic: "/əˈveɪləbl/" },
    { english: "avenue", chinese: "大街；林荫大道", phonetic: "/ˈævənuː/" },
    { english: "average", chinese: "平均；一般的", phonetic: "/ˈævərɪdʒ/" },
    { english: "avert", chinese: "避免，防止", phonetic: "/əˈvɜːrt/" },
    { english: "aviation", chinese: "航空；飞行术", phonetic: "/eɪviˈeɪʃən/" },
    { english: "avoid", chinese: "避免；躲避", phonetic: "/əˈvɔɪd/" },
    { english: "await", chinese: "等候，等待", phonetic: "/əˈweɪt/" },
    { english: "awake", chinese: "醒着的；觉醒", phonetic: "/əˈweɪk/" },
    { english: "award", chinese: "奖，奖品", phonetic: "/əˈwɔːrd/" },
    { english: "aware", chinese: "意识到的", phonetic: "/əˈwer/" },
    { english: "away", chinese: "离去，离开", phonetic: "/əˈweɪ/" },
    { english: "awful", chinese: "可怕的；极坏的", phonetic: "/ˈɔːfl/" },
    { english: "awfully", chinese: "可怕地；十分", phonetic: "/ˈɔːfli/" },
    { english: "awkward", chinese: "尴尬的；笨拙的", phonetic: "/ˈɔːkwərd/" },
    { english: "axis", chinese: "轴；轴线", phonetic: "/ˈæksɪs/" },
    
    // B字母词汇
    { english: "baby", chinese: "婴儿，婴孩", phonetic: "/ˈbeɪbi/" },
    { english: "bachelor", chinese: "学士；单身汉", phonetic: "/ˈbætʃələr/" },
    { english: "background", chinese: "背景", phonetic: "/ˈbækɡraʊnd/" },
    { english: "backward", chinese: "向后地，相反地", phonetic: "/ˈbækwərd/" },
    { english: "bacteria", chinese: "细菌", phonetic: "/bækˈtɪriə/" },
    { english: "badge", chinese: "徽章；证章", phonetic: "/bædʒ/" },
    { english: "badly", chinese: "非常；严重地", phonetic: "/ˈbædli/" },
    { english: "badminton", chinese: "羽毛球", phonetic: "/ˈbædmɪntən/" },
    { english: "baffle", chinese: "使困惑", phonetic: "/ˈbæfl/" },
    { english: "baggage", chinese: "行李；辎重", phonetic: "/ˈbæɡɪdʒ/" },
    { english: "bake", chinese: "烤，烘焙", phonetic: "/beɪk/" },
    { english: "balance", chinese: "平衡；余额", phonetic: "/ˈbæləns/" },
    { english: "bald", chinese: "光秃的", phonetic: "/bɔːld/" },
    { english: "ballet", chinese: "芭蕾舞", phonetic: "/bæˈleɪ/" },
    { english: "balloon", chinese: "气球", phonetic: "/bəˈluːn/" },
    { english: "banana", chinese: "香蕉", phonetic: "/bəˈnænə/" },
    { english: "bandage", chinese: "绷带", phonetic: "/ˈbændɪdʒ/" },
    { english: "bang", chinese: "重击；突然巨响", phonetic: "/bæŋ/" },
    { english: "bankrupt", chinese: "破产的", phonetic: "/ˈbæŋkrʌpt/" },
    { english: "banner", chinese: "旗帜，横幅", phonetic: "/ˈbænər/" },
    { english: "barber", chinese: "理发师", phonetic: "/ˈbɑːrbər/" },
    { english: "bare", chinese: "空的；赤裸的", phonetic: "/ber/" },
    { english: "barely", chinese: "仅仅，勉强", phonetic: "/ˈberli/" },
    { english: "bargain", chinese: "交易；便宜货", phonetic: "/ˈbɑːrɡən/" },
    { english: "bark", chinese: "树皮；狗叫", phonetic: "/bɑːrk/" },
    { english: "barn", chinese: "谷仓；畜棚", phonetic: "/bɑːrn/" },
    { english: "barrel", chinese: "桶；枪管", phonetic: "/ˈbærəl/" },
    { english: "barren", chinese: "贫瘠的", phonetic: "/ˈbærən/" },
    { english: "barrier", chinese: "障碍物，屏障", phonetic: "/ˈbæriər/" },
    { english: "basement", chinese: "地下室；地窖", phonetic: "/ˈbeɪsmənt/" },
    { english: "basic", chinese: "基本的；基础的", phonetic: "/ˈbeɪsɪk/" },
    { english: "basically", chinese: "主要地，基本上", phonetic: "/ˈbeɪsɪkli/" },
    { english: "basin", chinese: "水池；流域", phonetic: "/ˈbeɪsn/" },
    { english: "basis", chinese: "基础；底部", phonetic: "/ˈbeɪsɪs/" },
    { english: "basket", chinese: "篮子", phonetic: "/ˈbæskɪt/" },
    { english: "basketball", chinese: "篮球", phonetic: "/ˈbæskɪtbɔːl/" },
    { english: "batch", chinese: "一批；一炉", phonetic: "/bætʃ/" },
    { english: "bath", chinese: "沐浴；浴室", phonetic: "/bæθ/" },
    { english: "bathe", chinese: "沐浴；洗澡", phonetic: "/beɪð/" },
    { english: "bathroom", chinese: "浴室；厕所", phonetic: "/ˈbɑːθruːm/" },
    { english: "battery", chinese: "电池，蓄电池", phonetic: "/ˈbætəri/" },
    { english: "battle", chinese: "战役；斗争", phonetic: "/ˈbætl/" },
    { english: "bay", chinese: "海湾；狗吠声", phonetic: "/beɪ/" },
    { english: "beach", chinese: "海滩；湖滨", phonetic: "/biːtʃ/" },
    { english: "beam", chinese: "横梁；光线", phonetic: "/biːm/" },
    { english: "bean", chinese: "豆；嘴峰", phonetic: "/biːn/" },
    { english: "bear", chinese: "熊；负担", phonetic: "/ber/" },
    { english: "beard", chinese: "胡须", phonetic: "/bɪrd/" },
    { english: "bearing", chinese: "轴承；关系", phonetic: "/ˈberɪŋ/" },
    { english: "beast", chinese: "野兽；畜生", phonetic: "/biːst/" },
    { english: "beat", chinese: "敲打，拍子", phonetic: "/biːt/" },
    { english: "beautiful", chinese: "美丽的", phonetic: "/ˈbjuːtɪfl/" },
    { english: "beauty", chinese: "美；美丽", phonetic: "/ˈbjuːti/" },
    
    // B字母词汇续
    { english: "because", chinese: "因为，由于", phonetic: "/bɪˈkɔːz/" },
    { english: "become", chinese: "成为；变得", phonetic: "/bɪˈkʌm/" },
    { english: "bee", chinese: "蜜蜂，蜂", phonetic: "/biː/" },
    { english: "beef", chinese: "牛肉；肌肉", phonetic: "/biːf/" },
    { english: "beer", chinese: "啤酒", phonetic: "/bɪr/" },
    { english: "before", chinese: "在前，以前", phonetic: "/bɪˈfɔːr/" },
    { english: "beforehand", chinese: "事先；预先", phonetic: "/bɪˈfɔːrhænd/" },
    { english: "beggar", chinese: "乞丐；穷人", phonetic: "/ˈbeɡər/" },
    { english: "begin", chinese: "开始", phonetic: "/bɪˈɡɪn/" },
    { english: "beginner", chinese: "初学者；新手", phonetic: "/bɪˈɡɪnər/" },
    { english: "beginning", chinese: "开始；起点", phonetic: "/bɪˈɡɪnɪŋ/" },
    { english: "behalf", chinese: "代表；利益", phonetic: "/bɪˈhæf/" },
    { english: "behave", chinese: "表现；举止", phonetic: "/bɪˈheɪv/" },
    { english: "behavior", chinese: "行为，举止", phonetic: "/bɪˈheɪvjər/" },
    { english: "behind", chinese: "在后地", phonetic: "/bɪˈhaɪnd/" },
    { english: "being", chinese: "存在；生物", phonetic: "/ˈbiːɪŋ/" },
    { english: "belief", chinese: "相信，信赖", phonetic: "/bɪˈliːf/" },
    { english: "believe", chinese: "相信；认为", phonetic: "/bɪˈliːv/" },
    { english: "bell", chinese: "铃，钟", phonetic: "/bel/" },
    { english: "belong", chinese: "属于", phonetic: "/bɪˈlɔːŋ/" },
    { english: "beloved", chinese: "心爱的", phonetic: "/bɪˈlʌvd/" },
    { english: "below", chinese: "在下面", phonetic: "/bɪˈloʊ/" },
    { english: "belt", chinese: "带；腰带", phonetic: "/belt/" },
    { english: "bench", chinese: "长凳；工作台", phonetic: "/bentʃ/" },
    { english: "bend", chinese: "弯曲", phonetic: "/bend/" },
    { english: "beneath", chinese: "在……之下", phonetic: "/bɪˈniːθ/" },
    { english: "beneficial", chinese: "有益的", phonetic: "/benɪˈfɪʃəl/" },
    { english: "benefit", chinese: "利益，好处", phonetic: "/ˈbenɪfɪt/" },
    { english: "berry", chinese: "浆果", phonetic: "/ˈberi/" },
    { english: "beside", chinese: "在旁边", phonetic: "/bɪˈsaɪd/" },
    { english: "besides", chinese: "除……之外", phonetic: "/bɪˈsaɪdz/" },
    { english: "best", chinese: "最好的", phonetic: "/best/" },
    { english: "betray", chinese: "背叛；出卖", phonetic: "/bɪˈtreɪ/" },
    { english: "better", chinese: "较好的", phonetic: "/ˈbetər/" },
    { english: "between", chinese: "在……之间", phonetic: "/bɪˈtwiːn/" },
    { english: "bewilder", chinese: "使迷惑", phonetic: "/bɪˈwɪldər/" },
    { english: "beyond", chinese: "在……较远的一边", phonetic: "/bɪˈjɑːnd/" },
    { english: "bias", chinese: "偏见；偏爱", phonetic: "/ˈbaɪəs/" },
    { english: "Bible", chinese: "圣经", phonetic: "/ˈbaɪbl/" },
    { english: "bibliography", chinese: "参考书目", phonetic: "/bɪbliˈɑːɡrəfi/" },
    { english: "bicycle", chinese: "自行车", phonetic: "/ˈbaɪsɪkl/" },
    { english: "bike", chinese: "自行车", phonetic: "/baɪk/" },
    { english: "bill", chinese: "法案；账单", phonetic: "/bɪl/" },
    { english: "billion", chinese: "十亿", phonetic: "/ˈbɪljən/" },
    { english: "bind", chinese: "结合；装订", phonetic: "/baɪnd/" },
    { english: "biography", chinese: "传记；档案", phonetic: "/baɪˈɑːɡrəfi/" },
    { english: "biology", chinese: "生物学", phonetic: "/baɪˈɑːlədʒi/" },
    { english: "bird", chinese: "鸟", phonetic: "/bɜːrd/" },
    { english: "birth", chinese: "出生；血统", phonetic: "/bɜːrθ/" },
    { english: "birthday", chinese: "生日，诞辰", phonetic: "/ˈbɜːrθdeɪ/" },
    { english: "biscuit", chinese: "小点心，饼干", phonetic: "/ˈbɪskɪt/" },
    { english: "bite", chinese: "咬；刺痛", phonetic: "/baɪt/" },
    { english: "bitter", chinese: "苦的；痛苦的", phonetic: "/ˈbɪtər/" },
    { english: "bitterly", chinese: "苦涩地，悲痛地", phonetic: "/ˈbɪtərli/" },
    { english: "bizarre", chinese: "奇异的", phonetic: "/bɪˈzɑːr/" },
    { english: "blackboard", chinese: "黑板", phonetic: "/ˈblækbɔːrd/" },
    { english: "blade", chinese: "叶片；刀片", phonetic: "/bleɪd/" },
    { english: "blame", chinese: "责备；归咎于", phonetic: "/bleɪm/" },
    { english: "blank", chinese: "空白的", phonetic: "/blæŋk/" },
    { english: "blanket", chinese: "毛毯，毯子", phonetic: "/ˈblæŋkɪt/" },
    { english: "blast", chinese: "爆炸；冲击波", phonetic: "/blæst/" },
    { english: "blaze", chinese: "火焰，光辉", phonetic: "/bleɪz/" },
    { english: "bleak", chinese: "阴冷的；荒凉的", phonetic: "/bliːk/" },
    { english: "bleed", chinese: "使出血；流血", phonetic: "/bliːd/" },
    { english: "blend", chinese: "混合", phonetic: "/blend/" },
    { english: "bless", chinese: "祝福；保佑", phonetic: "/bles/" },
    { english: "blind", chinese: "盲目的；瞎的", phonetic: "/blaɪnd/" },
    { english: "block", chinese: "块；街区", phonetic: "/blɑːk/" },
    { english: "blood", chinese: "血，血液", phonetic: "/blʌd/" },
    { english: "bloom", chinese: "花；青春", phonetic: "/bluːm/" },
    { english: "blossom", chinese: "花；开花期", phonetic: "/ˈblɑːsəm/" },
    { english: "blow", chinese: "殴打；风吹", phonetic: "/bloʊ/" },
    { english: "blue", chinese: "蓝色", phonetic: "/bluː/" },
    { english: "blunder", chinese: "愚蠢的错误", phonetic: "/ˈblʌndər/" },
    { english: "blunt", chinese: "钝的，不锋利的", phonetic: "/blʌnt/" },
    { english: "blush", chinese: "脸红；羞愧", phonetic: "/blʌʃ/" },
    { english: "board", chinese: "董事会；木板", phonetic: "/bɔːrd/" },
    { english: "boast", chinese: "夸口说", phonetic: "/boʊst/" },
    { english: "boat", chinese: "小船；轮船", phonetic: "/boʊt/" },
    { english: "body", chinese: "身体；主体", phonetic: "/ˈbɑːdi/" },
    { english: "boil", chinese: "沸点，沸腾", phonetic: "/bɔɪl/" },
    { english: "bold", chinese: "大胆的，英勇的", phonetic: "/boʊld/" },
    { english: "bolt", chinese: "门闩，螺钉", phonetic: "/boʊlt/" },
    { english: "bomb", chinese: "炸弹", phonetic: "/bɑːm/" },
    { english: "bond", chinese: "债券；结合", phonetic: "/bɑːnd/" },
    { english: "bone", chinese: "骨；骨骼", phonetic: "/boʊn/" },
    { english: "bonus", chinese: "奖金；红利", phonetic: "/ˈboʊnəs/" },
    { english: "book", chinese: "书籍；卷", phonetic: "/bʊk/" },
    { english: "boom", chinese: "繁荣，隆隆声", phonetic: "/buːm/" },
    { english: "boost", chinese: "促进；增加", phonetic: "/buːst/" },
    { english: "boot", chinese: "靴，靴子", phonetic: "/buːt/" },
    { english: "booth", chinese: "货摊；电话亭", phonetic: "/buːθ/" },
    { english: "border", chinese: "边境；边界", phonetic: "/ˈbɔːrdər/" },
    { english: "bore", chinese: "钻孔；使烦扰", phonetic: "/bɔːr/" },
    { english: "born", chinese: "出生；天生的", phonetic: "/bɔːrn/" },
    { english: "borrow", chinese: "借；借用", phonetic: "/ˈbɑːroʊ/" },
    { english: "bosom", chinese: "胸；胸怀", phonetic: "/ˈbʊzəm/" },
    { english: "boss", chinese: "老板；首领", phonetic: "/bɔːs/" },
    { english: "both", chinese: "双方，两者", phonetic: "/boʊθ/" },
    { english: "bother", chinese: "烦扰，打扰", phonetic: "/ˈbɑːðər/" },
    { english: "bottle", chinese: "瓶子", phonetic: "/ˈbɑːtl/" },
    { english: "bottom", chinese: "底部；末端", phonetic: "/ˈbɑːtəm/" },
    { english: "bough", chinese: "大树枝", phonetic: "/baʊ/" },
    { english: "bounce", chinese: "反跳，弹起", phonetic: "/baʊns/" },
    { english: "bound", chinese: "跃进；范围", phonetic: "/baʊnd/" },
    { english: "boundary", chinese: "边界；范围", phonetic: "/ˈbaʊndri/" },
    { english: "bow", chinese: "弓；鞠躬", phonetic: "/baʊ/" },
    { english: "bowl", chinese: "碗；木球", phonetic: "/boʊl/" },
    { english: "box", chinese: "盒子，箱", phonetic: "/bɑːks/" },
    { english: "boy", chinese: "男孩；男人", phonetic: "/bɔɪ/" },
    { english: "boycott", chinese: "联合抵制", phonetic: "/ˈbɔɪkɑːt/" },
    { english: "brace", chinese: "支柱，带子", phonetic: "/breɪs/" },
    { english: "bracket", chinese: "支架；括号", phonetic: "/ˈbrækɪt/" },
    { english: "brain", chinese: "头脑，智力", phonetic: "/breɪn/" },
    { english: "brake", chinese: "刹车；阻碍", phonetic: "/breɪk/" },
    { english: "branch", chinese: "分支；分公司", phonetic: "/bræntʃ/" },
    { english: "brand", chinese: "商标，牌子", phonetic: "/brænd/" },
    { english: "brandy", chinese: "白兰地酒", phonetic: "/ˈbrændi/" },
    { english: "brass", chinese: "黄铜", phonetic: "/bræs/" },
    { english: "brave", chinese: "勇敢的", phonetic: "/breɪv/" },
    { english: "Brazil", chinese: "巴西", phonetic: "/brəˈzɪl/" },
    { english: "Brazilian", chinese: "巴西的", phonetic: "/brəˈzɪliən/" },
    { english: "bread", chinese: "面包；生计", phonetic: "/bred/" },
    { english: "breadth", chinese: "宽度，幅度", phonetic: "/bredθ/" },
    { english: "break", chinese: "休息；打破", phonetic: "/breɪk/" },
    { english: "breakdown", chinese: "故障；崩溃", phonetic: "/ˈbreɪkdaʊn/" },
    { english: "breakfast", chinese: "早餐；早饭", phonetic: "/ˈbrekfəst/" },
    { english: "breast", chinese: "乳房，胸部", phonetic: "/brest/" },
    { english: "breath", chinese: "呼吸，气息", phonetic: "/breθ/" },
    { english: "breathe", chinese: "呼吸；低语", phonetic: "/briːð/" },
    { english: "breed", chinese: "繁殖；品种", phonetic: "/briːd/" },
    { english: "breeze", chinese: "微风", phonetic: "/briːz/" },
    { english: "bribe", chinese: "向……行贿", phonetic: "/braɪb/" },
    { english: "brick", chinese: "砖，砖块", phonetic: "/brɪk/" },
    { english: "bridge", chinese: "桥；桥牌", phonetic: "/brɪdʒ/" },
    { english: "brief", chinese: "简短的", phonetic: "/briːf/" },
    { english: "bright", chinese: "明亮的", phonetic: "/braɪt/" },
    { english: "brighten", chinese: "明亮；变亮", phonetic: "/ˈbraɪtn/" },
    { english: "brilliant", chinese: "灿烂的", phonetic: "/ˈbrɪliənt/" },
    { english: "brim", chinese: "边；边缘", phonetic: "/brɪm/" },
    { english: "bring", chinese: "带来；提供", phonetic: "/brɪŋ/" },
    { english: "brisk", chinese: "敏锐的，活泼的", phonetic: "/brɪsk/" },
    { english: "bristle", chinese: "刚毛；猪鬃", phonetic: "/ˈbrɪsl/" },
    { english: "Britain", chinese: "英国", phonetic: "/ˈbrɪtn/" },
    { english: "British", chinese: "英国的", phonetic: "/ˈbrɪtɪʃ/" },
    { english: "brittle", chinese: "易碎的，脆弱的", phonetic: "/ˈbrɪtl/" },
    { english: "broad", chinese: "宽的，辽阔的", phonetic: "/brɔːd/" },
    { english: "broadcast", chinese: "广播，播音", phonetic: "/ˈbrɔːdkæst/" },
    { english: "broken", chinese: "破碎的；坏掉的", phonetic: "/ˈbroʊkən/" },
    { english: "bronze", chinese: "青铜", phonetic: "/brɑːnz/" },
    { english: "brood", chinese: "一窝；沉思", phonetic: "/bruːd/" },
    { english: "brook", chinese: "小溪，小河", phonetic: "/brʊk/" },
    { english: "broom", chinese: "扫帚", phonetic: "/bruːm/" },
    { english: "brother", chinese: "兄弟；同事", phonetic: "/ˈbrʌðər/" },
    { english: "brow", chinese: "眉，眉毛", phonetic: "/braʊ/" },
    { english: "brown", chinese: "棕色的", phonetic: "/braʊn/" },
    { english: "bruise", chinese: "擦伤；挫伤", phonetic: "/bruːz/" },
    { english: "brush", chinese: "刷子；画笔", phonetic: "/brʌʃ/" },
    { english: "brutal", chinese: "残忍的", phonetic: "/ˈbruːtl/" },
    { english: "brute", chinese: "残忍的人", phonetic: "/bruːt/" },
    { english: "bubble", chinese: "气泡，泡沫", phonetic: "/ˈbʌbl/" },
    { english: "bucket", chinese: "桶，水桶", phonetic: "/ˈbʌkɪt/" },
    { english: "buckle", chinese: "带扣，搭钩", phonetic: "/ˈbʌkl/" },
    { english: "bud", chinese: "芽，萌芽", phonetic: "/bʌd/" },
    { english: "budget", chinese: "预算", phonetic: "/ˈbʌdʒɪt/" },
    { english: "buffer", chinese: "缓冲区", phonetic: "/ˈbʌfər/" },
    { english: "bug", chinese: "臭虫，小虫", phonetic: "/bʌɡ/" },
    { english: "build", chinese: "建立；建筑", phonetic: "/bɪld/" },
    { english: "building", chinese: "建筑；建筑物", phonetic: "/ˈbɪldɪŋ/" },
    { english: "bulb", chinese: "电灯泡；鳞茎", phonetic: "/bʌlb/" },
    { english: "bulk", chinese: "体积，容量", phonetic: "/bʌlk/" },
    { english: "bull", chinese: "公牛", phonetic: "/bʊl/" },
    { english: "bullet", chinese: "子弹", phonetic: "/ˈbʊlɪt/" },
    { english: "bulletin", chinese: "公告，公报", phonetic: "/ˈbʊlɪtɪn/" },
    { english: "bump", chinese: "肿块，隆起物", phonetic: "/bʌmp/" },
    { english: "bunch", chinese: "群；串", phonetic: "/bʌntʃ/" },
    { english: "bundle", chinese: "束；捆", phonetic: "/ˈbʌndl/" },
    { english: "burden", chinese: "负担；责任", phonetic: "/ˈbɜːrdn/" },
    { english: "bureau", chinese: "局，处", phonetic: "/ˈbjʊroʊ/" },
    { english: "bureaucracy", chinese: "官僚主义", phonetic: "/bjʊˈrɑːkrəsi/" },
    { english: "burial", chinese: "埋葬；葬礼", phonetic: "/ˈberiəl/" },
    { english: "burn", chinese: "燃烧；烧毁", phonetic: "/bɜːrn/" },
    { english: "burst", chinese: "爆裂，炸破", phonetic: "/bɜːrst/" },
    { english: "bury", chinese: "埋葬；隐藏", phonetic: "/ˈberi/" },
    { english: "bus", chinese: "公共汽车", phonetic: "/bʌs/" },
    { english: "bush", chinese: "灌木；矮树丛", phonetic: "/bʊʃ/" },
    { english: "business", chinese: "商业，交易", phonetic: "/ˈbɪznɪs/" },
    { english: "busy", chinese: "忙碌的", phonetic: "/ˈbɪzi/" },
    { english: "butcher", chinese: "屠夫；肉店", phonetic: "/ˈbʊtʃər/" },
    { english: "butter", chinese: "黄油，奶油", phonetic: "/ˈbʌtər/" },
    { english: "butterfly", chinese: "蝴蝶", phonetic: "/ˈbʌtərflaɪ/" },
    { english: "button", chinese: "按钮；纽扣", phonetic: "/ˈbʌtn/" },
    { english: "buy", chinese: "购买", phonetic: "/baɪ/" },
    { english: "buzz", chinese: "嗡嗡声", phonetic: "/bʌz/" },
    { english: "bypass", chinese: "旁路，支路", phonetic: "/ˈbaɪpæs/" },
    
    // C字母词汇
    { english: "cabbage", chinese: "卷心菜", phonetic: "/ˈkæbɪdʒ/" },
    { english: "cabin", chinese: "小屋；客舱", phonetic: "/ˈkæbɪn/" },
    { english: "cabinet", chinese: "内阁；橱柜", phonetic: "/ˈkæbɪnət/" },
    { english: "cable", chinese: "缆绳；电缆", phonetic: "/ˈkeɪbl/" },
    { english: "cafe", chinese: "咖啡馆", phonetic: "/kæˈfeɪ/" },
    { english: "cafeteria", chinese: "自助餐厅", phonetic: "/kæfəˈtɪriə/" },
    { english: "cage", chinese: "笼，兽笼", phonetic: "/keɪdʒ/" },
    { english: "cake", chinese: "蛋糕；块状物", phonetic: "/keɪk/" },
    { english: "calculate", chinese: "计算；预测", phonetic: "/ˈkælkjuleɪt/" },
    { english: "calculation", chinese: "计算；估计", phonetic: "/kælkjuˈleɪʃən/" },
    { english: "calculator", chinese: "计算器", phonetic: "/ˈkælkjuleɪtər/" },
    { english: "calendar", chinese: "日历；历法", phonetic: "/ˈkæləndər/" },
    { english: "call", chinese: "电话；呼叫", phonetic: "/kɔːl/" },
    { english: "calm", chinese: "静的，平静的", phonetic: "/kɑːm/" },
    { english: "camel", chinese: "骆驼", phonetic: "/ˈkæml/" },
    { english: "camera", chinese: "照相机", phonetic: "/ˈkæmrə/" },
    { english: "camp", chinese: "露营地", phonetic: "/kæmp/" },
    { english: "campaign", chinese: "运动；战役", phonetic: "/kæmˈpeɪn/" },
    { english: "campus", chinese: "校园", phonetic: "/ˈkæmpəs/" },
    { english: "Canada", chinese: "加拿大", phonetic: "/ˈkænədə/" },
    { english: "Canadian", chinese: "加拿大的", phonetic: "/kəˈneɪdiən/" },
    { english: "canal", chinese: "运河；水道", phonetic: "/kəˈnæl/" },
    { english: "cancel", chinese: "取消；删去", phonetic: "/ˈkænsl/" },
    { english: "cancer", chinese: "癌症", phonetic: "/ˈkænsər/" },
    { english: "candidate", chinese: "候选人", phonetic: "/ˈkændɪdət/" },
    { english: "candle", chinese: "蜡烛", phonetic: "/ˈkændl/" },
    { english: "candy", chinese: "糖果，巧克力", phonetic: "/ˈkændi/" },
    { english: "cannon", chinese: "大炮；加农炮", phonetic: "/ˈkænən/" },
    { english: "canoe", chinese: "独木舟", phonetic: "/kəˈnuː/" },
    { english: "canon", chinese: "标准；教规", phonetic: "/ˈkænən/" },
    { english: "canteen", chinese: "食堂；水壶", phonetic: "/kænˈtiːn/" },
    { english: "canvas", chinese: "帆布", phonetic: "/ˈkænvəs/" },
    { english: "cap", chinese: "盖；帽子", phonetic: "/kæp/" },
    { english: "capable", chinese: "有能力的", phonetic: "/ˈkeɪpəbl/" },
    { english: "capacity", chinese: "能力；容量", phonetic: "/kəˈpæsəti/" },
    { english: "capital", chinese: "首都；资金", phonetic: "/ˈkæpətl/" },
    { english: "capsule", chinese: "胶囊；太空舱", phonetic: "/ˈkæpsl/" },
    { english: "captain", chinese: "队长；船长", phonetic: "/ˈkæptən/" },
    { english: "captive", chinese: "被俘虏的", phonetic: "/ˈkæptɪv/" },
    { english: "capture", chinese: "俘获；夺得", phonetic: "/ˈkæptʃər/" },
    { english: "car", chinese: "汽车；车厢", phonetic: "/kɑːr/" },
    { english: "carbon", chinese: "碳", phonetic: "/ˈkɑːrbən/" },
    { english: "card", chinese: "卡片；纸牌", phonetic: "/kɑːrd/" },
    { english: "care", chinese: "关怀；照料", phonetic: "/ker/" },
    { english: "career", chinese: "生涯；职业", phonetic: "/kəˈrɪr/" },
    { english: "careful", chinese: "仔细的", phonetic: "/ˈkerfl/" },
    { english: "careless", chinese: "粗心的", phonetic: "/ˈkerləs/" },
    { english: "cargo", chinese: "货物，船货", phonetic: "/ˈkɑːrɡoʊ/" },
    { english: "carpenter", chinese: "木匠，木工", phonetic: "/ˈkɑːrpəntər/" },
    { english: "carpet", chinese: "地毯", phonetic: "/ˈkɑːrpət/" },
    
    // C字母词汇续
    { english: "confidence", chinese: "信心；信任", phonetic: "/ˈkɑːnfədəns/" },
    { english: "confident", chinese: "自信的", phonetic: "/ˈkɑːnfədənt/" },
    { english: "confidential", chinese: "机密的", phonetic: "/kɑːnfəˈdenʃəl/" },
    { english: "confine", chinese: "限制，禁闭", phonetic: "/kənˈfaɪn/" },
    { english: "confirm", chinese: "确认；确定", phonetic: "/kənˈfɜːrm/" },
    { english: "conflict", chinese: "冲突，矛盾", phonetic: "/ˈkɑːnflɪkt/" },
    { english: "conform", chinese: "符合；遵照", phonetic: "/kənˈfɔːrm/" },
    { english: "confront", chinese: "面对；遭遇", phonetic: "/kənˈfrʌnt/" },
    { english: "confuse", chinese: "使混乱", phonetic: "/kənˈfjuːz/" },
    { english: "confusion", chinese: "混淆，混乱", phonetic: "/kənˈfjuːʒən/" },
    { english: "congratulate", chinese: "祝贺；恭喜", phonetic: "/kənˈɡrætʃuleɪt/" },
    { english: "congress", chinese: "国会；代表大会", phonetic: "/ˈkɑːŋɡrəs/" },
    { english: "connect", chinese: "连接；联合", phonetic: "/kəˈnekt/" },
    { english: "connection", chinese: "连接；关系", phonetic: "/kəˈnekʃən/" },
    { english: "conquer", chinese: "战胜，征服", phonetic: "/ˈkɑːŋkər/" },
    { english: "conquest", chinese: "征服，战胜", phonetic: "/ˈkɑːŋkwest/" },
    { english: "conscience", chinese: "道德心，良心", phonetic: "/ˈkɑːnʃəns/" },
    { english: "conscious", chinese: "意识到的", phonetic: "/ˈkɑːnʃəs/" },
    { english: "consciousness", chinese: "意识；知觉", phonetic: "/ˈkɑːnʃəsnəs/" },
    { english: "consecutive", chinese: "连贯的", phonetic: "/kənˈsekjətɪv/" },
    { english: "consensus", chinese: "一致；舆论", phonetic: "/kənˈsensəs/" },
    { english: "consent", chinese: "同意，赞成", phonetic: "/kənˈsent/" },
    { english: "consequence", chinese: "结果；重要性", phonetic: "/ˈkɑːnsəkwens/" },
    { english: "consequently", chinese: "因此；结果", phonetic: "/ˈkɑːnsəkwentli/" },
    { english: "conservation", chinese: "保存，保持", phonetic: "/kɑːnsərˈveɪʃən/" },
    { english: "conservative", chinese: "保守的", phonetic: "/kənˈsɜːrvətɪv/" },
    { english: "consider", chinese: "考虑；认为", phonetic: "/kənˈsɪdər/" },
    { english: "considerable", chinese: "相当大的", phonetic: "/kənˈsɪdərəbl/" },
    { english: "consideration", chinese: "考虑；原因", phonetic: "/kənsɪdəˈreɪʃən/" },
    { english: "consist", chinese: "由…组成", phonetic: "/kənˈsɪst/" },
    { english: "consistent", chinese: "始终如一的", phonetic: "/kənˈsɪstənt/" },
    { english: "constant", chinese: "不变的", phonetic: "/ˈkɑːnstənt/" },
    { english: "constitution", chinese: "宪法；章程", phonetic: "/kɑːnstəˈtuːʃən/" },
    { english: "construct", chinese: "建造，构造", phonetic: "/kənˈstrʌkt/" },
    { english: "construction", chinese: "建设；建筑物", phonetic: "/kənˈstrʌkʃən/" },
    { english: "consult", chinese: "查阅；商量", phonetic: "/kənˈsʌlt/" },
    { english: "consume", chinese: "消耗，消费", phonetic: "/kənˈsuːm/" },
    { english: "consumer", chinese: "消费者", phonetic: "/kənˈsuːmər/" },
    { english: "contact", chinese: "接触，联系", phonetic: "/ˈkɑːntækt/" },
    { english: "contain", chinese: "包含；控制", phonetic: "/kənˈteɪn/" },
    { english: "container", chinese: "集装箱", phonetic: "/kənˈteɪnər/" },
    { english: "contemplate", chinese: "沉思；注视", phonetic: "/ˈkɑːntəmpleɪt/" },
    { english: "contemporary", chinese: "当代的", phonetic: "/kənˈtempəreri/" },
    { english: "content", chinese: "内容，目录", phonetic: "/ˈkɑːntent/" },
    { english: "contest", chinese: "论争，竞赛", phonetic: "/ˈkɑːntest/" },
    { english: "context", chinese: "环境；上下文", phonetic: "/ˈkɑːntekst/" },
    { english: "continent", chinese: "大陆，洲", phonetic: "/ˈkɑːntənənt/" },
    { english: "continue", chinese: "继续，延续", phonetic: "/kənˈtɪnjuː/" },
    { english: "continuous", chinese: "连续的", phonetic: "/kənˈtɪnjuəs/" },
    { english: "contract", chinese: "合同，契约", phonetic: "/ˈkɑːntrækt/" },
    { english: "contradict", chinese: "反驳；否定", phonetic: "/kɑːntrəˈdɪkt/" },
    { english: "contrary", chinese: "相反的", phonetic: "/ˈkɑːntreri/" },
    { english: "contrast", chinese: "对比，对照", phonetic: "/ˈkɑːntræst/" },
    { english: "contribute", chinese: "贡献，出力", phonetic: "/kənˈtrɪbjuːt/" },
    { english: "control", chinese: "控制；管理", phonetic: "/kənˈtroʊl/" },
    { english: "controversial", chinese: "有争议的", phonetic: "/kɑːntrəˈvɜːrʃəl/" },
    { english: "convenience", chinese: "便利；厕所", phonetic: "/kənˈviːniəns/" },
    { english: "convenient", chinese: "方便的", phonetic: "/kənˈviːniənt/" },
    { english: "convention", chinese: "大会；惯例", phonetic: "/kənˈvenʃən/" },
    { english: "conventional", chinese: "符合习俗的", phonetic: "/kənˈvenʃənl/" },
    { english: "conversation", chinese: "交谈，会话", phonetic: "/kɑːnvərˈseɪʃən/" },
    { english: "convert", chinese: "使转变", phonetic: "/kənˈvɜːrt/" },
    { english: "convince", chinese: "说服；使确信", phonetic: "/kənˈvɪns/" },
    { english: "cook", chinese: "做饭，烹调", phonetic: "/kʊk/" },
    { english: "cool", chinese: "凉爽的", phonetic: "/kuːl/" },
    { english: "cooperate", chinese: "合作，配合", phonetic: "/koʊˈɑːpəreɪt/" },
    { english: "coordinate", chinese: "调节，配合", phonetic: "/koʊˈɔːrdəneɪt/" },
    { english: "cope", chinese: "处理；对付", phonetic: "/koʊp/" },
    { english: "copper", chinese: "铜；铜币", phonetic: "/ˈkɑːpər/" },
    { english: "copy", chinese: "副本；一册", phonetic: "/ˈkɑːpi/" },
    { english: "cord", chinese: "绳索；束缚", phonetic: "/kɔːrd/" },
    { english: "core", chinese: "核心；要点", phonetic: "/kɔːr/" },
    { english: "corn", chinese: "玉米；谷物", phonetic: "/kɔːrn/" },
    { english: "corner", chinese: "角落，拐角处", phonetic: "/ˈkɔːrnər/" },
    { english: "corporate", chinese: "法人的", phonetic: "/ˈkɔːrpərət/" },
    { english: "corporation", chinese: "公司；法人", phonetic: "/kɔːrpəˈreɪʃən/" },
    { english: "correct", chinese: "正确的", phonetic: "/kəˈrekt/" },
    { english: "correspond", chinese: "符合，一致", phonetic: "/kɔːrəˈspɑːnd/" },
    { english: "corridor", chinese: "走廊", phonetic: "/ˈkɔːrədɔːr/" },
    { english: "corrupt", chinese: "腐败的", phonetic: "/kəˈrʌpt/" },
    { english: "cost", chinese: "成本，价钱", phonetic: "/kɔːst/" },
    { english: "costly", chinese: "昂贵的", phonetic: "/ˈkɔːstli/" },
    { english: "costume", chinese: "服装，装束", phonetic: "/ˈkɑːstuːm/" },
    { english: "cottage", chinese: "小屋；村舍", phonetic: "/ˈkɑːtədʒ/" },
    { english: "cotton", chinese: "棉花；棉布", phonetic: "/ˈkɑːtn/" },
    { english: "couch", chinese: "睡椅，长沙发", phonetic: "/kaʊtʃ/" },
    { english: "cough", chinese: "咳嗽", phonetic: "/kɔːf/" },
    { english: "council", chinese: "委员会", phonetic: "/ˈkaʊnsl/" },
    { english: "count", chinese: "数，计算", phonetic: "/kaʊnt/" },
    { english: "counter", chinese: "计算器；柜台", phonetic: "/ˈkaʊntər/" },
    { english: "country", chinese: "国家，国土", phonetic: "/ˈkʌntri/" },
    { english: "county", chinese: "郡，县", phonetic: "/ˈkaʊnti/" },
    { english: "couple", chinese: "对；夫妇", phonetic: "/ˈkʌpl/" },
    { english: "courage", chinese: "勇气；胆量", phonetic: "/ˈkɜːrədʒ/" },
    { english: "course", chinese: "科目；课程", phonetic: "/kɔːrs/" },
    { english: "court", chinese: "法院；球场", phonetic: "/kɔːrt/" },
    { english: "cousin", chinese: "堂兄弟姊妹", phonetic: "/ˈkʌzn/" },
    { english: "cover", chinese: "盖子，封面", phonetic: "/ˈkʌvər/" },
    { english: "cow", chinese: "奶牛，母牛", phonetic: "/kaʊ/" },
    { english: "crack", chinese: "裂缝，噼啪声", phonetic: "/kræk/" },
    { english: "craft", chinese: "工艺；手艺", phonetic: "/kræft/" },
    { english: "crash", chinese: "碰撞，坠落", phonetic: "/kræʃ/" },
    { english: "crazy", chinese: "疯狂的", phonetic: "/ˈkreɪzi/" },
    { english: "cream", chinese: "奶油，乳脂", phonetic: "/kriːm/" },
    { english: "create", chinese: "创造，创作", phonetic: "/kriˈeɪt/" },
    { english: "creative", chinese: "创造性的", phonetic: "/kriˈeɪtɪv/" },
    { english: "creature", chinese: "动物，生物", phonetic: "/ˈkriːtʃər/" },
    { english: "credit", chinese: "信用，信誉", phonetic: "/ˈkredɪt/" },
    { english: "crew", chinese: "队，组", phonetic: "/kruː/" },
    { english: "crime", chinese: "罪行，犯罪", phonetic: "/kraɪm/" },
    { english: "criminal", chinese: "犯罪的", phonetic: "/ˈkrɪmənl/" },
    { english: "crisis", chinese: "危机", phonetic: "/ˈkraɪsəs/" },
    { english: "critic", chinese: "批评家", phonetic: "/ˈkrɪtɪk/" },
    { english: "critical", chinese: "批评的", phonetic: "/ˈkrɪtɪkl/" },
    { english: "crop", chinese: "产量；农作物", phonetic: "/krɑːp/" },
    { english: "cross", chinese: "交叉，十字", phonetic: "/krɔːs/" },
    { english: "crowd", chinese: "人群；观众", phonetic: "/kraʊd/" },
    { english: "crown", chinese: "王冠；花冠", phonetic: "/kraʊn/" },
    { english: "crucial", chinese: "重要的", phonetic: "/ˈkruːʃəl/" },
    { english: "crude", chinese: "粗糙的", phonetic: "/kruːd/" },
    { english: "cruel", chinese: "残酷的", phonetic: "/ˈkruːəl/" },
    { english: "cruise", chinese: "巡游，巡航", phonetic: "/kruːz/" },
    { english: "crush", chinese: "压碎，压垮", phonetic: "/krʌʃ/" },
    { english: "cry", chinese: "哭，流泪", phonetic: "/kraɪ/" },
    { english: "crystal", chinese: "结晶，晶体", phonetic: "/ˈkrɪstl/" },
    { english: "cube", chinese: "立方；立方体", phonetic: "/kjuːb/" },
    { english: "culture", chinese: "文化，文明", phonetic: "/ˈkʌltʃər/" },
    { english: "cup", chinese: "杯子；奖杯", phonetic: "/kʌp/" },
    { english: "cure", chinese: "治愈，治疗", phonetic: "/kjʊr/" },
    { english: "curious", chinese: "好奇的", phonetic: "/ˈkjʊriəs/" },
    { english: "currency", chinese: "货币；通货", phonetic: "/ˈkɜːrənsi/" },
    { english: "current", chinese: "现在的", phonetic: "/ˈkɜːrənt/" },
    { english: "curve", chinese: "曲线；弯曲", phonetic: "/kɜːrv/" },
    { english: "custom", chinese: "习惯，惯例", phonetic: "/ˈkʌstəm/" },
    { english: "customer", chinese: "顾客", phonetic: "/ˈkʌstəmər/" },
    { english: "cut", chinese: "切，割", phonetic: "/kʌt/" },
    { english: "cycle", chinese: "循环；周期", phonetic: "/ˈsaɪkl/" },
    { english: "cylinder", chinese: "圆筒；汽缸", phonetic: "/ˈsɪləndər/" }
];

// 学习记录管理系统
class LearningTracker {
    constructor() {
        this.storageKey = 'vocabularyLearningData';
        this.learningData = this.loadLearningData();
        this.currentGameWords = [];
        this.gameResults = {};
    }
    
    // 加载学习数据
    loadLearningData() {
        const saved = localStorage.getItem(this.storageKey);
        if (saved) {
            return JSON.parse(saved);
        }
        
        // 初始化所有词汇的学习数据
        const initialData = {};
        allVocabulary.forEach(word => {
            initialData[word.english] = {
                totalSeen: 0,
                totalCorrect: 0,
                totalWrong: 0,
                recentGames: [], // 最近几次游戏的记录
                consecutiveCorrect: 0,
                lastSeen: null,
                weight: 1.0 // 选择权重，1.0为基准
            };
        });
        return initialData;
    }
    
    // 保存学习数据
    saveLearningData() {
        localStorage.setItem(this.storageKey, JSON.stringify(this.learningData));
    }
    
    // 开始新游戏，记录选中的单词
    startNewGame(selectedWords) {
        this.currentGameWords = selectedWords;
        this.gameResults = {};
        
        // 更新单词被选中次数
        selectedWords.forEach(word => {
            if (this.learningData[word.english]) {
                this.learningData[word.english].totalSeen++;
                this.learningData[word.english].lastSeen = Date.now();
            }
        });
    }
    
    // 记录单词配对结果
    recordWordResult(englishWord, isCorrect) {
        if (!this.learningData[englishWord]) return;
        
        const wordData = this.learningData[englishWord];
        
        if (isCorrect) {
            wordData.totalCorrect++;
            wordData.consecutiveCorrect++;
        } else {
            wordData.totalWrong++;
            wordData.consecutiveCorrect = 0;
        }
        
        // 记录到当前游戏结果
        this.gameResults[englishWord] = isCorrect;
    }
    
    // 游戏结束，更新权重和历史记录
    finishGame() {
        this.currentGameWords.forEach(word => {
            const wordData = this.learningData[word.english];
            const isCorrect = this.gameResults[word.english] || false;
            
            // 更新最近游戏记录（保留最近10次）
            wordData.recentGames.push(isCorrect);
            if (wordData.recentGames.length > 10) {
                wordData.recentGames.shift();
            }
            
            // 计算最近几次的正确率
            const recentCorrectRate = this.getRecentCorrectRate(word.english);
            
            // 根据正确率调整权重
            wordData.weight = this.calculateNewWeight(recentCorrectRate, wordData.consecutiveCorrect);
        });
        
        this.saveLearningData();
    }
    
    // 获取最近游戏的正确率
    getRecentCorrectRate(englishWord) {
        const wordData = this.learningData[englishWord];
        if (!wordData || wordData.recentGames.length === 0) return 0;
        
        const correctCount = wordData.recentGames.filter(result => result).length;
        return correctCount / wordData.recentGames.length;
    }
    
    // 计算新的权重
    calculateNewWeight(recentCorrectRate, consecutiveCorrect) {
        let weight = 1.0;
        
        // 如果最近正确率很高（>80%）且连续正确次数多，降低权重
        if (recentCorrectRate >= 0.8 && consecutiveCorrect >= 3) {
            weight = 0.3; // 大幅降低出现概率
        } else if (recentCorrectRate >= 0.7) {
            weight = 0.6; // 适度降低出现概率
        } else if (recentCorrectRate <= 0.3) {
            weight = 2.5; // 大幅提高出现概率
        } else if (recentCorrectRate <= 0.5) {
            weight = 1.8; // 适度提高出现概率
        }
        
        return weight;
    }
    
    // 智能选择词汇（基于权重）
    selectWordsForGame() {
        // 创建加权词汇池
        const weightedVocabulary = [];
        
        allVocabulary.forEach(word => {
            const wordData = this.learningData[word.english];
            const weight = wordData ? wordData.weight : 1.0;
            
            // 根据权重添加到池中（权重越高，添加次数越多）
            const copies = Math.max(1, Math.round(weight * 10));
            for (let i = 0; i < copies; i++) {
                weightedVocabulary.push(word);
            }
        });
        
        // 从加权池中随机选择18个不重复的词汇
        const selected = [];
        const usedWords = new Set();
        
        while (selected.length < 18 && weightedVocabulary.length > 0) {
            const randomIndex = Math.floor(Math.random() * weightedVocabulary.length);
            const word = weightedVocabulary[randomIndex];
            
            if (!usedWords.has(word.english)) {
                selected.push(word);
                usedWords.add(word.english);
            }
            
            // 移除所有该单词的副本
            for (let i = weightedVocabulary.length - 1; i >= 0; i--) {
                if (weightedVocabulary[i].english === word.english) {
                    weightedVocabulary.splice(i, 1);
                }
            }
        }
        
        return selected;
    }
    
    // 获取学习统计信息
    getLearningStats() {
        let totalWords = 0;
        let masteredWords = 0;
        let strugglingWords = 0;
        
        Object.values(this.learningData).forEach(wordData => {
            if (wordData.totalSeen > 0) {
                totalWords++;
                const correctRate = wordData.totalCorrect / (wordData.totalCorrect + wordData.totalWrong);
                
                if (correctRate >= 0.8 && wordData.consecutiveCorrect >= 3) {
                    masteredWords++;
                } else if (correctRate <= 0.4) {
                    strugglingWords++;
                }
            }
        });
        
        return {
            totalWords,
            masteredWords,
            strugglingWords,
            accuracy: totalWords > 0 ? (masteredWords / totalWords * 100).toFixed(1) : 0
        };
    }
}

// 全局学习追踪器实例
const learningTracker = new LearningTracker();

// 智能生成游戏网格数据
function generateGameGrid() {
    const vocabulary = learningTracker.selectWordsForGame();
    const gridData = [];
    
    // 开始新游戏记录
    learningTracker.startNewGame(vocabulary);
    
    // 添加英文单词和中文释义
    vocabulary.forEach(word => {
        gridData.push({
            id: `en_${word.english}`,
            type: 'english',
            content: word.english,
            phonetic: word.phonetic || null,
            match: `zh_${word.english}`,
            wordData: word
        });
        gridData.push({
            id: `zh_${word.english}`,
            type: 'chinese',
            content: word.chinese,
            match: `en_${word.english}`,
            wordData: word
        });
    });
    
    // 随机打乱顺序
    return gridData.sort(() => Math.random() - 0.5);
}

// 获取提示（显示一个配对）
function getHint(gridData) {
    const unmatched = gridData.filter(cell => !cell.matched);
    if (unmatched.length >= 2) {
        const english = unmatched.find(cell => cell.type === 'english');
        if (english) {
            const chinese = unmatched.find(cell => cell.id === english.match);
            if (chinese) {
                return [english.id, chinese.id];
            }
        }
    }
    return null;
}

// 记录配对结果到学习追踪器
function recordPairResult(englishWord, isCorrect) {
    learningTracker.recordWordResult(englishWord, isCorrect);
}

// 游戏结束时调用
function finishGameTracking() {
    learningTracker.finishGame();
}