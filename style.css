/* CSS变量定义 */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ecdc4;
    --warning-color: #ffe66d;
    --danger-color: #ff6b6b;
    --dark-color: #2c3e50;
    --light-color: #f8f9fa;
    --border-radius: 12px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 12px 48px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--dark-color);
}

/* 游戏容器 */
.game-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 32px;
    box-shadow: var(--shadow);
    max-width: 600px;
    width: 100%;
    text-align: center;
}

/* 游戏头部 */
.game-header {
    margin-bottom: 24px;
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 32px;
    margin-bottom: 16px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
}

/* 游戏说明 */
.game-instructions {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    border-radius: var(--border-radius);
    padding: 16px;
    margin-bottom: 24px;
    color: white;
    font-weight: 500;
}

/* 游戏网格 */
.game-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-bottom: 24px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 游戏格子 */
.game-cell {
    aspect-ratio: 1;
    background: white;
    border-radius: var(--border-radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    padding: 4px;
    transition: var(--transition);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 2px solid transparent;
    word-break: break-all;
    line-height: 1.2;
    position: relative;
    overflow: hidden;
}

.cell-main {
    font-weight: 700;
    font-size: inherit;
    margin-bottom: 2px;
}

.cell-phonetic {
    font-size: 0.6rem;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    font-style: italic;
    line-height: 1;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.game-cell:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.game-cell.selected {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: scale(1.05);
    border-color: var(--accent-color);
}

.game-cell.selected .cell-phonetic {
    color: rgba(255, 255, 255, 0.8);
}

.game-cell.matched {
    background: linear-gradient(135deg, var(--success-color), #26de81);
    color: white;
    transform: scale(0.9);
    opacity: 0.8;
    pointer-events: none;
}

.game-cell.matched .cell-phonetic {
    color: rgba(255, 255, 255, 0.7);
}

.game-cell.error {
    background: linear-gradient(135deg, var(--danger-color), #ff5757);
    color: white;
    animation: shake 0.5s ease-in-out;
}

.game-cell.error .cell-phonetic {
    color: rgba(255, 255, 255, 0.8);
}

.game-cell.english {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.game-cell.english .cell-phonetic {
    color: rgba(255, 255, 255, 0.8);
}

.game-cell.chinese {
    background: linear-gradient(135deg, #fd79a8, #e84393);
    color: white;
}

.game-cell.empty {
    background: transparent;
    box-shadow: none;
    pointer-events: none;
}

/* 按钮样式 */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-hint {
    background: linear-gradient(135deg, var(--warning-color), #f39c12);
    color: var(--dark-color);
}

.btn-info {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

.btn:disabled,
.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover,
.btn.disabled:hover {
    transform: none !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 游戏消息弹窗 */
.game-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.message-content {
    background: white;
    padding: 32px;
    border-radius: 24px;
    text-align: center;
    box-shadow: var(--shadow);
    max-width: 400px;
    width: 90%;
}

.message-content h3 {
    font-size: 1.75rem;
    margin-bottom: 16px;
    color: var(--dark-color);
}

.message-content p {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.6;
}

/* 选择指示器 */
.selection-indicator {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 24px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    z-index: 999;
    opacity: 0;
    transition: var(--transition);
}

.selection-indicator.show {
    opacity: 1;
}

/* 动画效果 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes glow {
    0%, 100% { 
        box-shadow: 0 0 5px var(--primary-color);
    }
    50% { 
        box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--accent-color);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

@keyframes sparkle {
    0%, 100% { 
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
    25% { 
        opacity: 0.7;
        transform: scale(1.1) rotate(90deg);
    }
    50% { 
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
    75% { 
        opacity: 0.7;
        transform: scale(1.1) rotate(270deg);
    }
}

.game-cell.new {
    animation: fadeInScale 0.4s ease-out;
}

.game-cell.hint {
    animation: glow 1s ease-in-out infinite;
}

.game-cell.correct {
    animation: bounce 0.6s ease-out;
}

.game-cell.disappear {
    animation: zoomOut 0.3s ease-in forwards;
}

.stat-value.updated {
    animation: bounce 0.5s ease-out;
}

/* 粒子效果容器 */
.particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: sparkle 1s ease-out forwards;
}

/* 连击效果 */
.combo-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--success-color), var(--accent-color));
    color: white;
    padding: 16px 32px;
    border-radius: 20px;
    font-size: 1.5rem;
    font-weight: 700;
    z-index: 1001;
    animation: fadeInScale 0.3s ease-out;
    pointer-events: none;
}

/* 响应式设计 - 针对6.78英寸手机优化 (2780x1264) */
@media (max-width: 768px) {
    body {
        padding: 8px;
        min-height: 100vh;
        align-items: flex-start;
    }

    .game-container {
        padding: 16px;
        max-width: 100%;
        margin: 0;
        border-radius: 16px;
        min-height: calc(100vh - 16px);
        display: flex;
        flex-direction: column;
    }

    .game-title {
        font-size: 1.8rem;
        margin-bottom: 12px;
    }

    .game-stats {
        gap: 20px;
        margin-bottom: 12px;
        flex-wrap: wrap;
    }

    .stat-item {
        min-width: 80px;
    }

    .stat-value {
        font-size: 1.4rem;
        font-weight: 700;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .game-instructions {
        font-size: 0.85rem;
        padding: 8px 12px;
        margin-bottom: 12px;
    }

    .game-grid {
        gap: 4px;
        padding: 8px;
        flex: 1;
        max-height: 60vh;
        overflow: visible;
    }

    .game-cell {
        font-size: 0.7rem;
        padding: 3px;
        border-radius: 8px;
        min-height: 60px;
    }

    .cell-main {
        font-size: 0.75rem;
        line-height: 1.1;
    }

    .cell-phonetic {
        font-size: 0.55rem;
        margin-top: 2px;
    }

    .game-controls {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 12px;
        justify-content: center;
    }

    .btn {
        padding: 12px 16px;
        font-size: 0.8rem;
        flex: 1;
        min-width: 120px;
        max-width: 160px;
    }
}

/* 针对6.78英寸手机的特殊优化 */
@media (max-width: 480px) and (min-height: 800px) {
    .game-container {
        padding: 12px;
        min-height: calc(100vh - 24px);
    }

    .game-title {
        font-size: 1.6rem;
        margin-bottom: 8px;
    }

    .game-stats {
        gap: 15px;
        margin-bottom: 8px;
    }

    .stat-value {
        font-size: 1.3rem;
    }

    .game-grid {
        gap: 3px;
        padding: 6px;
        max-height: 65vh;
    }

    .game-cell {
        font-size: 0.65rem;
        padding: 2px;
        min-height: 55px;
        border-radius: 6px;
    }

    .cell-main {
        font-size: 0.7rem;
    }

    .cell-phonetic {
        font-size: 0.5rem;
    }

    .game-controls {
        gap: 6px;
        margin-top: 8px;
    }

    .btn {
        padding: 10px 12px;
        font-size: 0.75rem;
        min-width: 100px;
        max-width: 140px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 380px) {
    .game-cell {
        font-size: 0.6rem;
        padding: 1px;
        min-height: 50px;
    }

    .cell-main {
        font-size: 0.65rem;
    }

    .cell-phonetic {
        font-size: 0.45rem;
    }

    .game-grid {
        gap: 2px;
    }

    .btn {
        min-width: 90px;
        max-width: 120px;
        font-size: 0.7rem;
    }
}

/* 错误词汇记录样式 */
.wrong-words-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    z-index: 1000;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.wrong-words-content {
    background: white;
    border-radius: 16px;
    padding: 24px;
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.wrong-words-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f0f0f0;
}

.wrong-words-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 4px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #f0f0f0;
    color: #666;
}

.wrong-words-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.wrong-word-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    border-left: 4px solid var(--danger-color);
    transition: var(--transition);
}

.wrong-word-item:hover {
    background: #f0f0f0;
    transform: translateX(4px);
}

.wrong-word-english {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.wrong-word-phonetic {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    margin-bottom: 6px;
}

.wrong-word-chinese {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 8px;
}

.wrong-word-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #888;
}

.wrong-count {
    background: var(--danger-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.empty-wrong-words {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-wrong-words .emoji {
    font-size: 3rem;
    margin-bottom: 16px;
    display: block;
}

/* 游戏结束面板优化 */
.game-complete-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-play-again {
    background: linear-gradient(135deg, var(--success-color), #45b7aa);
    color: white;
    flex: 1;
    min-width: 140px;
}

.btn-view-wrong {
    background: linear-gradient(135deg, var(--warning-color), #f39c12);
    color: var(--dark-color);
    flex: 1;
    min-width: 140px;
}

/* 手机端错误词汇面板优化 */
@media (max-width: 768px) {
    .wrong-words-panel {
        padding: 10px;
    }

    .wrong-words-content {
        padding: 16px;
        max-height: 85vh;
        border-radius: 12px;
    }

    .wrong-words-title {
        font-size: 1.3rem;
    }

    .wrong-word-item {
        padding: 12px;
    }

    .wrong-word-english {
        font-size: 1rem;
    }

    .wrong-word-phonetic {
        font-size: 0.8rem;
    }

    .wrong-word-chinese {
        font-size: 0.85rem;
    }

    .game-complete-actions {
        flex-direction: column;
        gap: 8px;
    }

    .btn-play-again,
    .btn-view-wrong {
        min-width: auto;
        width: 100%;
    }
}